# Dependency Optimization and Conflict Resolution

## Overview

This document describes the dependency optimization performed to resolve version conflicts and reduce bundle size in the yew-it-tools project.

## Problem

The CI/CD pipeline was showing multiple warnings about duplicate crate versions, particularly for `gloo` and its sub-crates:

```
warning[duplicate]: found 3 duplicate entries for crate 'gloo'
warning[duplicate]: found 2 duplicate entries for crate 'gloo-console'
warning[duplicate]: found 2 duplicate entries for crate 'gloo-events'
warning[duplicate]: found 2 duplicate entries for crate 'gloo-timers'
...
```

## Root Cause

The issue was caused by:

1. **Redundant direct dependencies**: The project was directly importing individual `gloo` sub-crates that were already included in the main `gloo` crate
2. **Version conflicts**: Yew 0.21 and yew-router 0.18 depend on older versions of `gloo` (0.8.1, 0.10.0) while the project was using `gloo` 0.11.0

## Solution

### 1. Removed Redundant Dependencies

**Before (Cargo.toml):**
```toml
gloo = { version = "0.11", features = ["storage"] }
gloo-storage = "0.3"
gloo-events = "0.2"
gloo-timers = "0.3"
```

**After (Cargo.toml):**
```toml
gloo = { version = "0.11", features = ["storage", "events", "timers"] }
```

### 2. Updated Import Statements

Updated all source files to use unified `gloo` imports:

- `gloo_storage::` → `gloo::storage::`
- `gloo_events::` → `gloo::events::`
- `gloo_timers::` → `gloo::timers::`

**Files Updated:**
- `src/components/collapsible_tool_menu.rs`
- `src/ui/select.rs`
- `src/ui/text_copyable.rs`

### 3. Updated Security Configuration

Modified `deny.toml` to acknowledge the remaining version conflicts that are caused by Yew's dependencies:

```toml
[bans]
multiple-versions = "warn"
skip = [
    { name = "gloo", version = "0.8.1" },
    { name = "gloo", version = "0.10.0" },
    # ... other gloo sub-crates
]
```

## Results

### ✅ Benefits Achieved

1. **Reduced Redundancy**: Eliminated 3 redundant direct dependencies
2. **Cleaner Dependency Tree**: Simplified the dependency graph
3. **Maintained Functionality**: All features continue to work as expected
4. **CI/CD Compliance**: Security checks now pass without warnings
5. **Better Maintainability**: Unified imports are easier to manage

### ⚠️ Remaining Conflicts

Some version conflicts remain due to Yew's dependency constraints:

- `gloo` 0.8.1 (via prokio → yew)
- `gloo` 0.10.0 (via yew, yew-router)
- `gloo` 0.11.0 (our direct dependency)

These conflicts are **expected and acceptable** because:
- They're caused by framework dependencies beyond our control
- They don't affect functionality
- They're properly documented in `deny.toml`
- They will be resolved when Yew updates to newer gloo versions

## Future Improvements

1. **Monitor Yew Updates**: Watch for Yew releases that update to gloo 0.11+
2. **Regular Dependency Audits**: Run `cargo tree --duplicates` monthly
3. **Automated Checks**: The CI/CD pipeline now properly handles these conflicts

## Commands for Maintenance

```bash
# Check for duplicate dependencies
cargo tree --duplicates

# Update dependencies
cargo update

# Run security checks
cargo deny check

# Build and test
cargo check && cargo clippy
```

## Related Files

- `Cargo.toml` - Main dependency configuration
- `deny.toml` - Security and licensing configuration
- `src/components/collapsible_tool_menu.rs` - Updated imports
- `src/ui/select.rs` - Updated imports
- `src/ui/text_copyable.rs` - Updated imports
