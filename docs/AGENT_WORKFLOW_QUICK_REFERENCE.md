# Agent Workflow Quick Reference

## 🚀 Quick Commands

### Issue Management
```bash
# Create issue
gh issue create --title "Type: Brief description" --body "Detailed description"

# Comment on issue
gh issue comment <number> --body "Update message"

# Close issue
gh issue close <number> --comment "Resolution message"

# List issues
gh issue list
```

### Branch Operations
```bash
# Feature branch (from dev)
git checkout dev && git pull origin dev
git checkout -b feature/name-issue-##

# Hotfix branch (from main)
git checkout main && git pull origin main
git checkout -b hotfix/name-issue-##

# Bugfix branch (from dev)
git checkout dev && git pull origin dev
git checkout -b bugfix/name-issue-##
```

### Commit & Push
```bash
# Standard commit
git add <files>
git commit -m "type: brief description

- Detailed change 1
- Detailed change 2

Fixes #issue-number"

# Push branch
git push origin <branch-name>
```

### Pull Requests
```bash
# Create PR
gh pr create --title "type: Brief description" --body "Detailed description" --base <target-branch>

# Target branches:
# Features: --base dev
# Hotfixes: --base main
# Bugfixes: --base dev
```

## 📋 Naming Conventions

### Issue Titles
- `Bug: Description of the issue`
- `Feature: Description of the feature`
- `Enhancement: Description of the improvement`
- `Docs: Description of documentation need`
- `Performance: Description of optimization`

### Branch Names
- `feature/short-description-issue-##`
- `hotfix/short-description-issue-##`
- `bugfix/short-description-issue-##`

### Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `hotfix`: Critical production fix
- `docs`: Documentation
- `style`: Code formatting
- `refactor`: Code restructuring
- `test`: Testing
- `chore`: Maintenance
- `perf`: Performance

## ✅ Pre-Commit Checklist
- [ ] `cargo fmt` (code formatting)
- [ ] `cargo clippy` (linting)
- [ ] `cargo check` (compilation)
- [ ] `./build.sh dev` (development build)
- [ ] `./build.sh github-pages` (production build)
- [ ] Issue referenced in commit
- [ ] Descriptive commit message

## 🔄 Workflow Steps

### Standard Feature/Bug Fix
1. **Create Issue** → 2. **Create Branch** → 3. **Implement** → 4. **Test** → 5. **Commit** → 6. **Push** → 7. **Create PR**

### Emergency Hotfix
1. **Create URGENT Issue** → 2. **Hotfix Branch from Main** → 3. **Minimal Fix** → 4. **Test** → 5. **Commit** → 6. **Push** → 7. **PR to Main**

## 🚫 Never Do
- Direct commits to main/dev
- Vague commit messages
- Skip testing
- Large unfocused commits
- Forget issue references

## 📞 When to Ask for Help
- Unsure about implementation
- Breaking changes needed
- Security implications
- Major architectural decisions
- Performance concerns

---
**Full Guide**: See `docs/AGENT_WORKFLOW_GUIDE.md` for complete instructions.
