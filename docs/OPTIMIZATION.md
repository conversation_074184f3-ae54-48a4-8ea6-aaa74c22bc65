# Bundle Size Optimization Guide

This guide provides comprehensive strategies for optimizing bundle sizes in the Yew IT Tools project to maintain fast loading times and prevent performance regressions.

## Current Bundle Size Limits

The CI/CD pipeline enforces these thresholds:

- **WASM Bundle**: 2MB (2048KB)
- **JavaScript Bundle**: 512KB
- **CSS Bundle**: 256KB

## WASM Optimization

### Cargo.toml Configuration

Our current release profile is optimized for size:

```toml
[profile.release]
opt-level = "z"          # Optimize for size
lto = true               # Link-time optimization
codegen-units = 1        # Single codegen unit for better optimization
panic = "abort"          # Smaller panic handling
strip = true             # Strip debug symbols
debug = false            # No debug info
```

### Code-Level Optimizations

1. **Minimize Dependencies**
   ```bash
   # Audit dependencies
   cargo tree
   
   # Remove unused dependencies
   cargo machete
   ```

2. **Use Feature Flags**
   ```toml
   # Only include needed features
   serde = { version = "1.0", features = ["derive"] }
   web-sys = { version = "0.3", features = ["Document", "Element"] }
   ```

3. **Optimize String Usage**
   ```rust
   // Prefer &str over String when possible
   fn process_text(text: &str) -> &str { text }
   
   // Use Cow for conditional ownership
   use std::borrow::Cow;
   fn maybe_modify(text: &str, modify: bool) -> Cow<str> {
       if modify {
           Cow::Owned(text.to_uppercase())
       } else {
           Cow::Borrowed(text)
       }
   }
   ```

4. **Minimize Allocations**
   ```rust
   // Use iterators instead of collecting
   let result: Vec<_> = items
       .iter()
       .filter(|item| item.is_valid())
       .map(|item| item.process())
       .collect();
   
   // Better: process without intermediate collection
   for item in items.iter().filter(|item| item.is_valid()) {
       process_item(item);
   }
   ```

### WASM-Specific Optimizations

1. **Use wasm-opt**
   ```bash
   # Install wasm-opt
   npm install -g wasm-opt
   
   # Optimize WASM file
   wasm-opt -Oz --enable-bulk-memory input.wasm -o output.wasm
   ```

2. **Enable WASM Features**
   ```toml
   [dependencies]
   wasm-bindgen = { version = "0.2", features = ["enable-interning"] }
   ```

3. **Minimize wasm-bindgen Overhead**
   ```rust
   // Use #[wasm_bindgen(skip_typescript)]
   #[wasm_bindgen(skip_typescript)]
   pub fn internal_function() {}
   
   // Minimize string conversions
   #[wasm_bindgen]
   pub fn process_data(data: &[u8]) -> Vec<u8> {
       // Process bytes directly
       data.iter().map(|&b| b.wrapping_add(1)).collect()
   }
   ```

## JavaScript Optimization

### Tree Shaking

1. **ES Modules**
   ```javascript
   // Use named imports for better tree shaking
   import { specificFunction } from 'library';
   
   // Avoid default imports of large libraries
   import * as library from 'library'; // ❌ Bad
   import { needed } from 'library';    // ✅ Good
   ```

2. **Dynamic Imports**
   ```javascript
   // Load features on demand
   async function loadFeature() {
       const { feature } = await import('./feature.js');
       return feature;
   }
   ```

### Minimize Runtime Dependencies

1. **Avoid Large Libraries**
   ```bash
   # Check bundle impact before adding dependencies
   npm install --save-dev webpack-bundle-analyzer
   ```

2. **Use Native APIs**
   ```javascript
   // Use native fetch instead of axios
   fetch('/api/data')
       .then(response => response.json())
       .then(data => console.log(data));
   ```

## CSS Optimization

### Tailwind CSS Configuration

1. **Purge Unused Styles**
   ```css
   /* src/tailwind.config.css */
   @content "./src/**/*.rs";
   @content "./index.html";
   ```

2. **Minimize Custom CSS**
   ```css
   /* Use Tailwind utilities instead of custom CSS */
   .custom-button {
       @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded;
   }
   ```

### PostCSS Optimization

1. **Enable Minification**
   ```javascript
   // postcss.config.mjs
   export default {
       plugins: {
           tailwindcss: {},
           autoprefixer: {},
           ...(process.env.NODE_ENV === 'production' ? { cssnano: {} } : {})
       }
   };
   ```

2. **Remove Unused Prefixes**
   ```javascript
   // Configure autoprefixer for target browsers
   autoprefixer: {
       overrideBrowserslist: ['> 1%', 'last 2 versions']
   }
   ```

## Asset Optimization

### SVG Optimization

1. **Use SVGO**
   ```bash
   # Optimize SVG files
   ./build.sh optimize-svg
   
   # Manual optimization
   svgo --input input.svg --output output.svg
   ```

2. **Inline Small SVGs**
   ```rust
   // Inline small icons directly in Rust
   const ICON_SVG: &str = r#"<svg>...</svg>"#;
   ```

### Image Optimization

1. **Use Appropriate Formats**
   - WebP for photos
   - SVG for icons and simple graphics
   - PNG for images with transparency

2. **Optimize Image Sizes**
   ```bash
   # Use responsive images
   <img src="image-320w.webp" 
        srcset="image-320w.webp 320w, image-640w.webp 640w"
        sizes="(max-width: 320px) 280px, 640px" />
   ```

## Build Process Optimization

### Compression

1. **Enable Gzip/Brotli**
   ```bash
   # Automatic compression in build script
   find dist -type f \( -name "*.js" -o -name "*.css" -o -name "*.wasm" \) \
       -exec gzip -9 -k {} \; \
       -exec brotli -9 {} \;
   ```

2. **Configure Server Headers**
   ```nginx
   # nginx configuration
   location ~* \.(js|css|wasm)$ {
       gzip_static on;
       brotli_static on;
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

### Caching Strategy

1. **File Hashing**
   ```toml
   # Trunk.toml
   [build]
   filehash = true  # Enable content-based hashing
   ```

2. **Cache Headers**
   ```html
   <!-- Long-term caching for hashed assets -->
   <link rel="stylesheet" href="styles-abc123.css">
   ```

## Monitoring and Analysis

### Bundle Analysis Tools

1. **Cargo Bloat**
   ```bash
   cargo install cargo-bloat
   cargo bloat --release --crates
   ```

2. **WASM Analysis**
   ```bash
   # Analyze WASM binary
   wasm-objdump -h target/wasm32-unknown-unknown/release/app.wasm
   ```

3. **Size Tracking**
   ```bash
   # Track size over time
   echo "$(date): $(du -sh dist)" >> size-history.log
   ```

### Performance Metrics

1. **Core Web Vitals**
   - Largest Contentful Paint (LCP) < 2.5s
   - First Input Delay (FID) < 100ms
   - Cumulative Layout Shift (CLS) < 0.1

2. **Bundle Size Metrics**
   - Total bundle size
   - Individual asset sizes
   - Compression ratios
   - Load time impact

## Optimization Checklist

### Before Each Release

- [ ] Run `cargo bloat` to identify large dependencies
- [ ] Check bundle sizes against thresholds
- [ ] Verify compression is working
- [ ] Test loading performance on slow connections
- [ ] Review and remove unused dependencies
- [ ] Optimize new assets (SVGs, images)

### Regular Maintenance

- [ ] Update dependencies monthly
- [ ] Review bundle size trends quarterly
- [ ] Audit and remove unused code
- [ ] Optimize critical rendering path
- [ ] Monitor Core Web Vitals

## Emergency Size Reduction

If bundle size exceeds limits:

1. **Immediate Actions**
   ```bash
   # Re-run optimizations
   ./build.sh optimize-wasm
   ./build.sh optimize-svg
   
   # Check for debug builds
   cargo build --release
   ```

2. **Code Splitting**
   ```rust
   // Split large components
   use yew::suspense::use_future;
   
   #[function_component]
   fn LazyComponent() -> Html {
       let data = use_future(|| async { load_data().await });
       // Render based on data state
   }
   ```

3. **Feature Flags**
   ```toml
   [features]
   default = ["basic"]
   basic = []
   advanced = ["heavy-dependency"]
   ```

## Resources

- [Rust Performance Book](https://nnethercote.github.io/perf-book/)
- [WASM Optimization Guide](https://rustwasm.github.io/book/reference/code-size.html)
- [Tailwind CSS Optimization](https://tailwindcss.com/docs/optimizing-for-production)
- [Web Performance Best Practices](https://web.dev/performance/)

Remember: Optimization is an ongoing process. Regular monitoring and incremental improvements are more effective than sporadic large optimizations.
