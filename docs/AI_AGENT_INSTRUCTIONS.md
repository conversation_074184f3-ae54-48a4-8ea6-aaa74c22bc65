# AI Agent Instructions

## 🚀 Essential Rules

### Environment & Tools
- Use `nix-shell` for active nix environment
- Use `pnpm` for frontend package management (never npm/yarn)
- Always keep code clean - remove unused imports, variables, and redundant code to avoid bugs
- Follow proper GitHub workflow (see AGENT_WORKFLOW_GUIDE.md for details)

### Technology Stack
**UI and Styling:**
- Tailwind CSS: Utility-first CSS framework (version 3)
- @tailwindcss/typography: Typography plugin for Tailwind CSS (0.5.10)
- @tailwindcss/vite: Tailwind CSS integration for Vite (^4.0.0)
- PostCSS: CSS transformation tool (^8.5.3)
- Autoprefixer: Adds vendor prefixes to CSS (^10.4.21)

**Architecture:**
- Yew components should only handle UI rendering, user interactions, and calling WebAssembly functions
- Keep business logic in Rust backend/WASM
- Maintain clear separation between UI and logic layers

### Code Quality
- Run `cargo fmt` before every commit
- Run `cargo clippy` and fix all warnings
- Remove unused dependencies and imports
- Eliminate redundant code patterns
- Use latest Rust edition (2024)
- Follow conventional commit messages

### Package Management
- **Always use package managers** instead of manually editing package files
- JavaScript/Node.js: Use `pnpm install`, `pnpm add`, `pnpm remove`
- Rust: Use `cargo add`, `cargo remove`
- Never manually edit package.json, Cargo.toml dependencies

### Build & Deployment
- Test both `./build.sh dev` and `./build.sh github-pages` before committing
- Ensure GitHub Pages deployment works correctly
- Verify asset paths work for both local and GitHub Pages
- Check bundle size limits (WASM: 2MB, JS: 512KB, CSS: 256KB)

### Workflow Requirements
- Create issues before implementing features/fixes
- Use proper branch naming: `feature/name-issue-##`, `hotfix/name-issue-##`, `bugfix/name-issue-##`
- Never commit directly to main/dev branches
- Always create PRs with detailed descriptions
- Reference issues in commits: `Fixes #issue-number`

### Communication
- When tool calls fail due to size limitations: break down into smaller chunks, continue with implementation, ask for prioritization if needed
- Don't apologize for technical limitations - focus on solutions
- Ask specific questions when clarification is needed
- Provide progress updates on long-running tasks

### Error Handling
- If a build fails, investigate and fix the root cause
- If tests fail, fix the code, don't skip tests
- If linting fails, fix the issues, don't ignore warnings
- If deployment fails, check asset paths and configuration

### Performance
- Optimize for bundle size and loading speed
- Use tree-shaking and code splitting where appropriate
- Minimize dependencies and remove unused code
- Test performance impact of changes

### Security
- Never commit sensitive information
- Use secure coding practices
- Keep dependencies updated
- Follow security best practices for web applications

## 🔄 Quick Workflow

1. **Create Issue** → 2. **Branch from dev/main** → 3. **Implement** → 4. **Test & Clean** → 5. **Commit** → 6. **Push** → 7. **Create PR**

## ❌ Never Do
- Direct commits to main/dev
- Manual package file editing
- Skip testing/linting
- Leave unused code
- Vague commit messages
- Large unfocused commits

## ✅ Always Do
- Use package managers
- Clean up unused code
- Test builds before committing
- Follow naming conventions
- Reference issues in commits
- Break down large tasks

---
**Full Documentation**: See `docs/AGENT_WORKFLOW_GUIDE.md` for complete workflow instructions.
