# Agent GitHub Workflow Guide

This document provides comprehensive instructions for AI agents to follow proper GitHub workflow when working on the yew-it-tools project.

## 🔄 Core Workflow Principles

### Branch Strategy
- **main**: Production-ready code, protected branch
- **dev**: Development integration branch
- **feature/***: New features (from dev)
- **hotfix/***: Critical production fixes (from main)
- **bugfix/***: Bug fixes (from dev)

### Workflow Rules
1. **Never commit directly to main or dev**
2. **Always create branches for changes**
3. **Follow proper naming conventions**
4. **Create issues before implementing**
5. **Use pull requests for all changes**

## 📋 Step-by-Step Workflow

### 1. Issue Creation

**When to create an issue:**
- New feature requests
- Bug reports
- Enhancement proposals
- Documentation updates
- Performance improvements

**Issue Creation Commands:**
```bash
# Create a new issue
gh issue create --title "Clear, descriptive title" --body "Detailed description with:
- Problem description
- Expected behavior
- Current behavior
- Steps to reproduce (for bugs)
- Acceptance criteria
- Priority level"

# Add labels (if available)
gh issue create --title "Title" --body "Description" --label "bug,high-priority"
```

**Issue Title Format:**
- **Bug**: `Bug: Brief description of the issue`
- **Feature**: `Feature: Brief description of the feature`
- **Enhancement**: `Enhancement: Brief description of the improvement`
- **Documentation**: `Docs: Brief description of documentation need`
- **Performance**: `Performance: Brief description of optimization`

### 2. Branch Creation

**Branch Naming Convention:**
```bash
# Feature branches (from dev)
git checkout dev
git pull origin dev
git checkout -b feature/short-descriptive-name-issue-##

# Hotfix branches (from main)
git checkout main
git pull origin main
git checkout -b hotfix/short-descriptive-name-issue-##

# Bugfix branches (from dev)
git checkout dev
git pull origin dev
git checkout -b bugfix/short-descriptive-name-issue-##
```

**Examples:**
```bash
git checkout -b feature/command-palette-enhancement-issue-17
git checkout -b hotfix/github-pages-asset-paths-issue-23
git checkout -b bugfix/search-input-focus-issue-45
```

### 3. Development Process

**Before Making Changes:**
```bash
# Ensure you're on the correct branch
git branch

# Pull latest changes
git pull origin <branch-name>

# Check repository status
git status
```

**During Development:**
```bash
# Make frequent, atomic commits
git add <specific-files>
git commit -m "type: brief description

- Detailed change 1
- Detailed change 2
- Reference to issue if applicable

Fixes #issue-number"
```

**Commit Message Format:**
```
type: brief description (50 chars max)

- Detailed bullet point 1
- Detailed bullet point 2
- Technical details if needed

Fixes #issue-number
```

**Commit Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks
- `perf`: Performance improvements
- `hotfix`: Critical production fixes

### 4. Testing and Validation

**Before Committing:**
```bash
# Run code quality checks
cargo fmt
cargo clippy
cargo check

# Run tests (if available)
cargo test

# Build for different targets
./build.sh dev
./build.sh github-pages

# Check for issues
git status
git diff
```

### 5. Pull Request Creation

**PR Creation Commands:**
```bash
# Push branch to remote
git push origin <branch-name>

# Create pull request
gh pr create --title "type: Brief description" --body "## Description

Brief summary of changes made.

## Changes Made
- ✅ Change 1
- ✅ Change 2
- ✅ Change 3

## Testing
- ✅ Test 1 passed
- ✅ Test 2 passed
- ✅ Build successful

## Impact
- Describe the impact of changes
- Any breaking changes
- Performance implications

## Related Issues
Fixes #issue-number
Related to #other-issue

## Checklist
- [x] Code follows project style guidelines
- [x] Self-review completed
- [x] Tests added/updated
- [x] Documentation updated
- [x] No breaking changes (or documented)

## Screenshots (if applicable)
Add screenshots for UI changes" --base <target-branch>
```

**PR Title Format:**
```
type: Brief description of changes
```

**Base Branch Selection:**
- **Features**: `--base dev`
- **Hotfixes**: `--base main`
- **Bugfixes**: `--base dev`

### 6. Issue and PR Management

**Commenting on Issues:**
```bash
# Add progress updates
gh issue comment <issue-number> --body "## Progress Update

Current status: Working on implementation
- ✅ Completed task 1
- 🔄 Working on task 2
- ⏳ Pending task 3

ETA: [estimated completion time]"

# Add technical details
gh issue comment <issue-number> --body "## Technical Analysis

**Root Cause:** Description of the problem
**Proposed Solution:** Description of the fix
**Implementation Plan:**
1. Step 1
2. Step 2
3. Step 3

**Testing Strategy:**
- Test case 1
- Test case 2"
```

**Closing Issues:**
```bash
# Close with resolution
gh issue close <issue-number> --comment "## ✅ Issue Resolved

**Solution:** Brief description of how it was fixed
**PR:** #pr-number
**Testing:** Verification steps completed

The issue has been successfully resolved and deployed."
```

### 7. Emergency Hotfix Workflow

**For Critical Production Issues:**
```bash
# 1. Create issue immediately
gh issue create --title "URGENT: Critical issue description" --body "Critical production issue requiring immediate attention"

# 2. Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/critical-issue-description-issue-##

# 3. Implement minimal fix
# Make only necessary changes

# 4. Test thoroughly
./build.sh github-pages

# 5. Commit and push
git add .
git commit -m "hotfix: critical issue fix

Fixes #issue-number"
git push origin hotfix/critical-issue-description-issue-##

# 6. Create PR to main
gh pr create --title "hotfix: Critical issue fix" --body "Emergency fix for critical production issue" --base main

# 7. After merge, update dev branch
git checkout dev
git pull origin main
```

## 🚫 Common Mistakes to Avoid

1. **Direct commits to main/dev**
2. **Vague commit messages**
3. **Large, unfocused commits**
4. **Not referencing issues**
5. **Skipping testing**
6. **Not updating documentation**
7. **Creating PRs without descriptions**
8. **Not following naming conventions**

## ✅ Quality Checklist

Before any commit or PR:
- [ ] Code follows project style (`cargo fmt`)
- [ ] No linting errors (`cargo clippy`)
- [ ] Code compiles (`cargo check`)
- [ ] Tests pass (if applicable)
- [ ] Documentation updated
- [ ] Issue referenced in commit/PR
- [ ] Descriptive commit message
- [ ] Proper branch naming
- [ ] No sensitive information committed

## 🔧 Useful Commands Reference

```bash
# Repository status
git status
git branch -v
gh pr list
gh issue list

# Branch management
git checkout <branch>
git pull origin <branch>
git push origin <branch>

# GitHub CLI
gh issue create
gh issue comment <number>
gh issue close <number>
gh pr create
gh pr merge <number>
gh pr view <number>

# Build and test
./build.sh dev
./build.sh github-pages
cargo fmt
cargo clippy
cargo check
```

## 📞 When to Ask for Help

Contact the project maintainer when:
- Unsure about implementation approach
- Breaking changes are necessary
- Security implications exist
- Major architectural decisions needed
- Conflicts with existing features
- Performance concerns arise

## 📊 Workflow Examples

### Example 1: Feature Implementation
```bash
# 1. Create issue
gh issue create --title "Feature: Add dark mode toggle" --body "Implement dark mode toggle functionality for better user experience"

# 2. Create feature branch
git checkout dev
git pull origin dev
git checkout -b feature/dark-mode-toggle-issue-30

# 3. Implement feature
# ... make changes ...

# 4. Commit changes
git add src/components/theme_toggle.rs
git commit -m "feat: implement dark mode toggle component

- Add ThemeToggle component with state management
- Integrate with existing theme system
- Add keyboard shortcut support
- Update styling for both light and dark modes

Fixes #30"

# 5. Push and create PR
git push origin feature/dark-mode-toggle-issue-30
gh pr create --title "feat: Add dark mode toggle" --body "Implements dark mode toggle functionality as requested in #30" --base dev
```

### Example 2: Bug Fix
```bash
# 1. Create issue
gh issue create --title "Bug: Search input loses focus on mobile" --body "Search input field loses focus when typing on mobile devices"

# 2. Create bugfix branch
git checkout dev
git pull origin dev
git checkout -b bugfix/search-input-mobile-focus-issue-31

# 3. Fix bug
# ... make changes ...

# 4. Commit fix
git add src/components/search.rs
git commit -m "fix: prevent search input focus loss on mobile

- Add touch event handling for mobile devices
- Prevent default browser behavior on input focus
- Update CSS for better mobile interaction
- Add viewport meta tag optimization

Fixes #31"

# 5. Push and create PR
git push origin bugfix/search-input-mobile-focus-issue-31
gh pr create --title "fix: Search input focus on mobile" --body "Fixes search input focus issues on mobile devices" --base dev
```

### Example 3: Hotfix
```bash
# 1. Create urgent issue
gh issue create --title "URGENT: Production deployment failing" --body "GitHub Pages deployment is failing due to asset path issues"

# 2. Create hotfix branch
git checkout main
git pull origin main
git checkout -b hotfix/deployment-asset-paths-issue-32

# 3. Implement minimal fix
# ... make critical changes only ...

# 4. Commit hotfix
git add build.sh
git commit -m "hotfix: fix GitHub Pages asset path resolution

- Update build script to handle relative paths correctly
- Fix favicon and meta image loading on GitHub Pages
- Preserve Trunk asset management functionality

Fixes #32"

# 5. Push and create PR to main
git push origin hotfix/deployment-asset-paths-issue-32
gh pr create --title "hotfix: Fix GitHub Pages deployment" --body "Emergency fix for production deployment issues" --base main
```

## 🎯 Best Practices Summary

### Issue Management
- **Be Specific**: Clear, actionable issue titles and descriptions
- **Use Labels**: Categorize issues appropriately (bug, feature, enhancement)
- **Reference Related**: Link to related issues and PRs
- **Update Status**: Keep issues updated with progress

### Branch Management
- **Descriptive Names**: Use clear, consistent naming conventions
- **Single Purpose**: One feature/fix per branch
- **Clean History**: Rebase if necessary before merging
- **Delete After Merge**: Clean up merged branches

### Commit Practices
- **Atomic Commits**: Each commit should represent one logical change
- **Clear Messages**: Follow conventional commit format
- **Reference Issues**: Always link commits to relevant issues
- **Test Before Commit**: Ensure code works before committing

### Pull Request Excellence
- **Detailed Descriptions**: Explain what, why, and how
- **Self Review**: Review your own code before requesting review
- **Test Coverage**: Include testing information
- **Documentation**: Update docs when necessary

### Communication
- **Professional Tone**: Maintain respectful, professional communication
- **Clear Updates**: Provide regular progress updates on issues
- **Ask Questions**: Don't hesitate to seek clarification
- **Document Decisions**: Record important technical decisions

---

**Remember**: This workflow ensures code quality, traceability, and maintainability. Following these guidelines helps maintain a professional development environment and enables effective collaboration between AI agents and human developers.
