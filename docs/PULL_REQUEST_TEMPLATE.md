# Pull Request Template

## 📝 Description
Brief summary of the changes made and the problem they solve.

## 🔄 Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/formatting changes
- [ ] ♻️ Code refactoring
- [ ] ⚡ Performance improvements
- [ ] 🚨 Hotfix (critical production issue)

## 🛠️ Changes Made
- ✅ Change 1: Description
- ✅ Change 2: Description
- ✅ Change 3: Description

## 🧪 Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Build successful for development
- [ ] Build successful for GitHub Pages
- [ ] No linting errors (`cargo clippy`)
- [ ] Code formatted (`cargo fmt`)

### Test Results
```
# Include relevant test output or build results
```

## 📊 Impact
### Positive Impact
- Describe benefits of the changes
- Performance improvements
- User experience enhancements

### Potential Risks
- Any potential breaking changes
- Dependencies affected
- Migration requirements

## 🔗 Related Issues
- Fixes #issue-number
- Related to #issue-number
- Closes #issue-number

## 📸 Screenshots (if applicable)
<!-- Add screenshots for UI changes -->

## 📋 Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## 🔍 Review Notes
<!-- Any specific areas you'd like reviewers to focus on -->

## 📚 Additional Context
<!-- Add any other context about the pull request here -->

---

**For Reviewers:**
- Please check the implementation approach
- Verify test coverage is adequate
- Ensure documentation is updated
- Confirm no breaking changes (or properly documented)
