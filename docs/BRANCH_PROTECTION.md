# Branch Protection Rules Configuration

This document provides step-by-step instructions for configuring GitHub branch protection rules for the yew-it-tools repository to implement a robust development workflow that prevents bugs from reaching production.

## Overview

Our branch protection strategy implements a feature branch workflow: `feature/* → dev → main` with automated checks that must pass before merging.

## Required Branch Protection Rules

### 1. Main Branch Protection

Navigate to your repository settings: `Settings > Branches > Add rule`

**Branch name pattern:** `main`

**Protection settings:**
- ✅ **Require a pull request before merging**
  - ✅ Require approvals: `1`
  - ✅ Dismiss stale PR approvals when new commits are pushed
  - ✅ Require review from code owners (if CODEOWNERS file exists)
- ✅ **Require status checks to pass before merging**
  - ✅ Require branches to be up to date before merging
  - **Required status checks:**
    - `Lint and Code Quality`
    - `Security and Vulnerability Scan`
    - `Test and Coverage`
    - `Build and Performance Analysis`
- ✅ **Require conversation resolution before merging**
- ✅ **Restrict pushes that create files larger than 100MB**
- ✅ **Require signed commits** (recommended)
- ✅ **Include administrators** (applies rules to repository administrators)

### 2. Dev Branch Protection

**Branch name pattern:** `dev`

**Protection settings:**
- ✅ **Require a pull request before merging**
  - ✅ Require approvals: `1`
  - ✅ Dismiss stale PR approvals when new commits are pushed
- ✅ **Require status checks to pass before merging**
  - ✅ Require branches to be up to date before merging
  - **Required status checks:**
    - `Lint and Code Quality`
    - `Security and Vulnerability Scan`
    - `Test and Coverage`
    - `Build and Performance Analysis`
- ✅ **Require conversation resolution before merging**
- ✅ **Restrict pushes that create files larger than 100MB**

## Status Checks Explained

### Lint and Code Quality
- **Purpose:** Ensures code formatting and quality standards
- **Checks:**
  - Rust code formatting (`cargo fmt`)
  - Rust linting (`cargo clippy`)
  - Rust compilation (`cargo check`)
  - CSS/PostCSS configuration validation

### Security and Vulnerability Scan
- **Purpose:** Identifies security vulnerabilities in dependencies
- **Checks:**
  - Rust dependency audit (`cargo audit`)
  - License and security validation (`cargo deny`)
  - Node.js dependency audit (`pnpm audit`)

### Test and Coverage
- **Purpose:** Ensures code functionality and maintains test coverage
- **Checks:**
  - Unit tests (`cargo test`)
  - Code coverage analysis (`cargo llvm-cov`)
  - Coverage reporting to Codecov

### Build and Performance Analysis
- **Purpose:** Validates successful builds and prevents performance regressions
- **Checks:**
  - Successful Rust/WASM compilation
  - Bundle size regression testing
  - Build artifact optimization verification
  - Performance metrics analysis

## Development Workflow

### 1. Feature Development
```bash
# Start from dev branch
git checkout dev
git pull origin dev

# Create feature branch
git checkout -b feat/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push feature branch
git push origin feat/your-feature-name
```

### 2. Pull Request to Dev
1. Create PR from `feat/your-feature-name` to `dev`
2. Ensure all status checks pass
3. Request review from team member
4. Address review feedback
5. Merge after approval and passing checks

### 3. Release to Production
1. Create PR from `dev` to `main`
2. Ensure all status checks pass
3. Request review from maintainer
4. Merge after approval
5. Automatic deployment to GitHub Pages

## Bundle Size Thresholds

The CI/CD pipeline enforces the following bundle size limits to prevent performance regressions:

- **WASM Bundle:** 2MB (2048KB)
- **JavaScript Bundle:** 512KB
- **CSS Bundle:** 256KB

If your changes exceed these thresholds, the build will fail with specific guidance on optimization.

## Troubleshooting

### Status Check Failures

**Lint and Code Quality failures:**
```bash
# Fix formatting
cargo fmt

# Fix clippy warnings
cargo clippy --fix

# Validate CSS configuration
npx postcss --version
npx tailwindcss --help
```

**Security scan failures:**
```bash
# Update vulnerable dependencies
cargo update
pnpm update

# Check for security advisories
cargo audit
```

**Test failures:**
```bash
# Run tests locally
cargo test --all --verbose

# Check test coverage
cargo install cargo-llvm-cov
cargo llvm-cov --all-features --workspace
```

**Bundle size regression:**
```bash
# Analyze bundle sizes
pnpm build
du -sh dist/*

# Optimize WASM
./build.sh optimize-wasm

# Optimize SVG assets
./build.sh optimize-svg
```

## Emergency Procedures

### Hotfix Process
For critical production issues:

1. Create hotfix branch from `main`
2. Make minimal necessary changes
3. Create PR directly to `main`
4. Expedite review process
5. Merge after approval and passing checks
6. Backport changes to `dev` branch

### Bypassing Protection (Admin Only)
Repository administrators can bypass protection rules in emergencies:

1. Navigate to the failing PR
2. Click "Merge without waiting for requirements to be met"
3. Document the reason in the merge commit
4. Create follow-up issue to address bypassed checks

## Monitoring and Maintenance

### Regular Tasks
- Review and update bundle size thresholds quarterly
- Monitor security advisory notifications
- Update CI/CD dependencies monthly
- Review branch protection effectiveness

### Metrics to Track
- PR merge time
- Build failure rate
- Security vulnerability detection
- Bundle size trends
- Test coverage trends

## Additional Resources

- [GitHub Branch Protection Documentation](https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/defining-the-mergeability-of-pull-requests/about-protected-branches)
- [Rust Security Advisory Database](https://rustsec.org/)
- [Cargo Deny Documentation](https://embarkstudios.github.io/cargo-deny/)
- [Bundle Size Optimization Guide](./OPTIMIZATION.md)
