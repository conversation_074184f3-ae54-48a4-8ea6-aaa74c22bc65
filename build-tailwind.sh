#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

echo "Starting build-tailwind.sh script..."

# Create the output directories if they don't exist
echo "Creating output directories..."
mkdir -p dist/assets/css
mkdir -p dist/assets/icons

# Build Tailwind CSS with PostCSS and Autoprefixer in production mode
echo "Building Tailwind CSS..."
NODE_ENV=production npx postcss ./src/tailwind.css -o ./dist/tailwind.css
if [ $? -ne 0 ]; then
    echo "Error: Failed to build Tailwind CSS"
    exit 1
fi

# Skip CSS optimization for now to avoid build errors
echo "Skipping CSS optimization to ensure build stability."

# Check if we're in development mode (TRUNK_SERVE_ENV is set)
if [ -z "$TRUNK_SERVE_ENV" ]; then
    # We're in build mode, copy assets
    echo "Build mode: Copying assets..."

    # Create directories if they don't exist
    mkdir -p dist/assets/css
    mkdir -p dist/assets/icons

    # Copy CSS assets if the source directory exists and has files
    if [ -d "src/assets/css" ] && [ "$(ls -A src/assets/css 2>/dev/null)" ]; then
        cp -r src/assets/css/* dist/assets/css/ || echo "Warning: Some CSS assets could not be copied"
    else
        echo "Note: No CSS assets to copy or directory doesn't exist"
    fi

    # Copy icon assets if the source directory exists and has files
    if [ -d "src/assets/icons" ] && [ "$(ls -A src/assets/icons 2>/dev/null)" ]; then
        cp -r src/assets/icons/* dist/assets/icons/ || echo "Warning: Some icon assets could not be copied"
    else
        echo "Note: No icon assets to copy or directory doesn't exist"
    fi

    # Copy hero gradient SVG if it exists
    if [ -f "src/assets/hero-gradient.svg" ]; then
        mkdir -p dist/assets
        cp -r src/assets/hero-gradient.svg dist/assets/ || echo "Warning: Could not copy hero gradient SVG"
    else
        echo "Note: hero-gradient.svg not found"
    fi
else
    # We're in development mode, skip copying assets to avoid infinite rebuild loop
    echo "Development mode: Skipping asset copying to avoid rebuild loops"
fi

echo "build-tailwind.sh completed successfully"
