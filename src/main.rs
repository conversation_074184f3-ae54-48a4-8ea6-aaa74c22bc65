mod components;
mod config;
mod layouts;
mod pages;
mod router;
mod stores;
mod themes;
mod tools;
mod ui;
mod utils;
mod wasm;

use components::ThemeProvider;
use router::AppRouter;
use yew::prelude::*;

#[function_component(App)]
fn app() -> Html {
    html! {
        <>
            <ThemeProvider />
            <AppRouter />
        </>
    }
}

fn main() {
    wasm_logger::init(wasm_logger::Config::default());
    log::info!("Starting IT Tools application");

    yew::Renderer::<App>::new().render();
}
