use gloo::net::http::Request;
use js_sys::{Array, Object, Reflect};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::JsFuture;
use web_sys::{console, window, DomParser, SupportedType};

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VideoInfo {
    pub title: String,
    pub duration: String,
    pub thumbnail: String,
    pub qualities: Vec<VideoQuality>,
    pub video_id: String,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VideoQuality {
    pub quality: String,
    pub size: String,
    pub format: String,
    pub download_url: String,
    pub width: u32,
    pub height: u32,
}

#[derive(Debug)]
pub enum ExtractionError {
    NetworkError(String),
    ParseError(String),
    NotFound(String),
    Blocked(String),
}

impl std::fmt::Display for ExtractionError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ExtractionError::NetworkError(msg) => write!(f, "Network error: {}", msg),
            ExtractionError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            ExtractionError::NotFound(msg) => write!(f, "Not found: {}", msg),
            ExtractionError::Blocked(msg) => write!(f, "Blocked: {}", msg),
        }
    }
}

pub struct FacebookVideoExtractor {
    proxy_urls: Vec<String>,
    user_agents: Vec<String>,
}

impl FacebookVideoExtractor {
    pub fn new() -> Self {
        Self {
            proxy_urls: vec![
                "https://api.allorigins.win/get?url=".to_string(),
                "https://cors-anywhere.herokuapp.com/".to_string(),
                "https://api.codetabs.com/v1/proxy?quest=".to_string(),
            ],
            user_agents: vec![
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36".to_string(),
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36".to_string(),
            ],
        }
    }

    pub async fn extract_video_info(&self, url: &str) -> Result<VideoInfo, ExtractionError> {
        console::log_1(&format!("Extracting video info from: {}", url).into());

        // Validate Facebook URL
        if !self.is_valid_facebook_url(url) {
            return Err(ExtractionError::ParseError(
                "Invalid Facebook video URL".to_string(),
            ));
        }

        // Extract video ID
        let video_id = self.extract_video_id(url)?;
        console::log_1(&format!("Extracted video ID: {}", video_id).into());

        // Try different extraction methods
        match self.extract_via_graph_api(&video_id).await {
            Ok(info) => return Ok(info),
            Err(e) => console::log_1(&format!("Graph API failed: {}", e).into()),
        }

        match self.extract_via_html_parsing(url).await {
            Ok(info) => return Ok(info),
            Err(e) => console::log_1(&format!("HTML parsing failed: {}", e).into()),
        }

        match self.extract_via_oembed(url).await {
            Ok(info) => return Ok(info),
            Err(e) => console::log_1(&format!("oEmbed failed: {}", e).into()),
        }

        Err(ExtractionError::NotFound(
            "Could not extract video information using any method".to_string(),
        ))
    }

    fn is_valid_facebook_url(&self, url: &str) -> bool {
        let facebook_patterns = [
            r"facebook\.com/.*/(videos?|watch)",
            r"fb\.com/.*/(videos?|watch)",
            r"m\.facebook\.com/.*/(videos?|watch)",
            r"www\.facebook\.com/.*/(videos?|watch)",
        ];

        facebook_patterns
            .iter()
            .any(|pattern| Regex::new(pattern).unwrap().is_match(url))
    }

    fn extract_video_id(&self, url: &str) -> Result<String, ExtractionError> {
        // Try different video ID patterns
        let patterns = [
            r"facebook\.com/.*?/videos/(\d+)",
            r"facebook\.com/watch/?\?v=(\d+)",
            r"facebook\.com/.*?/posts/(\d+)",
            r"fb\.com/.*?/videos/(\d+)",
        ];

        for pattern in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(url) {
                    if let Some(id) = captures.get(1) {
                        return Ok(id.as_str().to_string());
                    }
                }
            }
        }

        Err(ExtractionError::ParseError(
            "Could not extract video ID from URL".to_string(),
        ))
    }

    async fn extract_via_graph_api(&self, video_id: &str) -> Result<VideoInfo, ExtractionError> {
        // Note: This requires a Facebook app token, which is not practical for client-side
        // This is here for completeness but will likely fail
        let graph_url = format!(
            "https://graph.facebook.com/v18.0/{}?fields=title,description,length,picture",
            video_id
        );

        match Request::get(&graph_url).send().await {
            Ok(response) => {
                if response.ok() {
                    match response.json::<serde_json::Value>().await {
                        Ok(data) => {
                            let title = data["title"]
                                .as_str()
                                .unwrap_or("Unknown Title")
                                .to_string();
                            let duration = self.format_duration(
                                data["length"].as_f64().unwrap_or(0.0) as u32,
                            );
                            let thumbnail = data["picture"]
                                .as_str()
                                .unwrap_or("")
                                .to_string();

                            Ok(VideoInfo {
                                title,
                                duration,
                                thumbnail,
                                qualities: vec![], // Graph API doesn't provide video URLs
                                video_id: video_id.to_string(),
                            })
                        }
                        Err(e) => Err(ExtractionError::ParseError(format!(
                            "Failed to parse Graph API response: {}",
                            e
                        ))),
                    }
                } else {
                    Err(ExtractionError::NetworkError(format!(
                        "Graph API request failed: {}",
                        response.status()
                    )))
                }
            }
            Err(e) => Err(ExtractionError::NetworkError(format!(
                "Graph API request error: {:?}",
                e
            ))),
        }
    }

    async fn extract_via_html_parsing(&self, url: &str) -> Result<VideoInfo, ExtractionError> {
        console::log_1(&"Attempting HTML parsing extraction".into());

        // Try with different proxy services
        for proxy_url in &self.proxy_urls {
            match self.fetch_with_proxy(url, proxy_url).await {
                Ok(html) => {
                    if let Ok(info) = self.parse_facebook_html(&html, url) {
                        return Ok(info);
                    }
                }
                Err(e) => {
                    console::log_1(&format!("Proxy {} failed: {}", proxy_url, e).into());
                    continue;
                }
            }
        }

        Err(ExtractionError::NetworkError(
            "All proxy attempts failed".to_string(),
        ))
    }

    async fn extract_via_oembed(&self, url: &str) -> Result<VideoInfo, ExtractionError> {
        let oembed_url = format!(
            "https://www.facebook.com/plugins/video/oembed.json/?url={}",
            urlencoding::encode(url)
        );

        match Request::get(&oembed_url).send().await {
            Ok(response) => {
                if response.ok() {
                    match response.json::<serde_json::Value>().await {
                        Ok(data) => {
                            let title = data["title"]
                                .as_str()
                                .unwrap_or("Unknown Title")
                                .to_string();
                            let thumbnail = data["thumbnail_url"]
                                .as_str()
                                .unwrap_or("")
                                .to_string();

                            Ok(VideoInfo {
                                title,
                                duration: "Unknown".to_string(),
                                thumbnail,
                                qualities: vec![],
                                video_id: self.extract_video_id(url).unwrap_or_default(),
                            })
                        }
                        Err(e) => Err(ExtractionError::ParseError(format!(
                            "Failed to parse oEmbed response: {}",
                            e
                        ))),
                    }
                } else {
                    Err(ExtractionError::NetworkError(format!(
                        "oEmbed request failed: {}",
                        response.status()
                    )))
                }
            }
            Err(e) => Err(ExtractionError::NetworkError(format!(
                "oEmbed request error: {:?}",
                e
            ))),
        }
    }

    async fn fetch_with_proxy(&self, url: &str, proxy_url: &str) -> Result<String, ExtractionError> {
        let proxied_url = format!("{}{}", proxy_url, urlencoding::encode(url));

        match Request::get(&proxied_url)
            .header("User-Agent", &self.user_agents[0])
            .send()
            .await
        {
            Ok(response) => {
                if response.ok() {
                    match response.text().await {
                        Ok(text) => Ok(text),
                        Err(e) => Err(ExtractionError::NetworkError(format!(
                            "Failed to read response text: {:?}",
                            e
                        ))),
                    }
                } else {
                    Err(ExtractionError::NetworkError(format!(
                        "Proxy request failed: {}",
                        response.status()
                    )))
                }
            }
            Err(e) => Err(ExtractionError::NetworkError(format!(
                "Proxy request error: {:?}",
                e
            ))),
        }
    }

    fn parse_facebook_html(&self, html: &str, original_url: &str) -> Result<VideoInfo, ExtractionError> {
        console::log_1(&"Parsing Facebook HTML".into());

        // Parse HTML using DomParser
        let window = window().unwrap();
        let document = window.document().unwrap();
        let parser = DomParser::new().unwrap();
        
        match parser.parse_from_string(html, SupportedType::TextHtml) {
            Ok(doc) => {
                // Extract title from meta tags
                let title = self.extract_meta_content(&doc, "og:title")
                    .or_else(|| self.extract_meta_content(&doc, "twitter:title"))
                    .unwrap_or_else(|| "Unknown Title".to_string());

                // Extract thumbnail
                let thumbnail = self.extract_meta_content(&doc, "og:image")
                    .or_else(|| self.extract_meta_content(&doc, "twitter:image"))
                    .unwrap_or_default();

                // Extract duration
                let duration = self.extract_meta_content(&doc, "video:duration")
                    .map(|d| self.format_duration(d.parse().unwrap_or(0)))
                    .unwrap_or_else(|| "Unknown".to_string());

                // Try to extract video URLs (this is the challenging part)
                let qualities = self.extract_video_urls_from_html(html);

                Ok(VideoInfo {
                    title,
                    duration,
                    thumbnail,
                    qualities,
                    video_id: self.extract_video_id(original_url).unwrap_or_default(),
                })
            }
            Err(e) => Err(ExtractionError::ParseError(format!(
                "Failed to parse HTML: {:?}",
                e
            ))),
        }
    }

    fn extract_meta_content(&self, doc: &web_sys::Document, property: &str) -> Option<String> {
        let meta_tags = doc.query_selector_all(&format!("meta[property='{}']", property)).ok()?;
        
        if meta_tags.length() > 0 {
            let meta_tag = meta_tags.get(0)?;
            let element = meta_tag.dyn_into::<web_sys::Element>().ok()?;
            element.get_attribute("content")
        } else {
            None
        }
    }

    fn extract_video_urls_from_html(&self, html: &str) -> Vec<VideoQuality> {
        let mut qualities = Vec::new();

        // Look for video URLs in various patterns
        let patterns = [
            r#""hd_src":"([^"]+)""#,
            r#""sd_src":"([^"]+)""#,
            r#""playable_url":"([^"]+)""#,
            r#""browser_native_hd_url":"([^"]+)""#,
            r#""browser_native_sd_url":"([^"]+)""#,
        ];

        for (i, pattern) in patterns.iter().enumerate() {
            if let Ok(regex) = Regex::new(pattern) {
                for captures in regex.captures_iter(html) {
                    if let Some(url_match) = captures.get(1) {
                        let url = url_match.as_str().replace("\\u0026", "&");
                        let quality = match i {
                            0 => "720p HD",
                            1 => "480p SD",
                            _ => "Unknown",
                        };

                        qualities.push(VideoQuality {
                            quality: quality.to_string(),
                            size: "Unknown".to_string(),
                            format: "MP4".to_string(),
                            download_url: url,
                            width: if i == 0 { 1280 } else { 854 },
                            height: if i == 0 { 720 } else { 480 },
                        });
                    }
                }
            }
        }

        qualities
    }

    fn format_duration(&self, seconds: u32) -> String {
        let hours = seconds / 3600;
        let minutes = (seconds % 3600) / 60;
        let secs = seconds % 60;

        if hours > 0 {
            format!("{}:{:02}:{:02}", hours, minutes, secs)
        } else {
            format!("{}:{:02}", minutes, secs)
        }
    }

    pub async fn download_video(&self, download_url: &str) -> Result<Vec<u8>, ExtractionError> {
        console::log_1(&format!("Downloading video from: {}", download_url).into());

        match Request::get(download_url)
            .header("User-Agent", &self.user_agents[0])
            .header("Referer", "https://www.facebook.com/")
            .send()
            .await
        {
            Ok(response) => {
                if response.ok() {
                    match response.binary().await {
                        Ok(data) => Ok(data),
                        Err(e) => Err(ExtractionError::NetworkError(format!(
                            "Failed to download video data: {:?}",
                            e
                        ))),
                    }
                } else {
                    Err(ExtractionError::NetworkError(format!(
                        "Video download failed: {}",
                        response.status()
                    )))
                }
            }
            Err(e) => Err(ExtractionError::NetworkError(format!(
                "Video download error: {:?}",
                e
            ))),
        }
    }
}

// Helper function to trigger file download in browser
#[wasm_bindgen]
pub fn trigger_download(data: &[u8], filename: &str, mime_type: &str) {
    use web_sys::{Blob, BlobPropertyBag, Url};
    
    let window = window().unwrap();
    let document = window.document().unwrap();
    
    // Create blob
    let array = js_sys::Uint8Array::new_with_length(data.len() as u32);
    array.copy_from(data);
    
    let mut blob_parts = Array::new();
    blob_parts.push(&array);
    
    let mut blob_property_bag = BlobPropertyBag::new();
    blob_property_bag.type_(mime_type);
    
    let blob = Blob::new_with_u8_array_sequence_and_options(&blob_parts, &blob_property_bag).unwrap();
    
    // Create download link
    let url = Url::create_object_url_with_blob(&blob).unwrap();
    let link = document.create_element("a").unwrap();
    link.set_attribute("href", &url).unwrap();
    link.set_attribute("download", filename).unwrap();
    link.set_attribute("style", "display: none").unwrap();
    
    document.body().unwrap().append_child(&link).unwrap();
    
    // Trigger download
    let html_link = link.dyn_into::<web_sys::HtmlElement>().unwrap();
    html_link.click();
    
    // Cleanup
    document.body().unwrap().remove_child(&link).unwrap();
    Url::revoke_object_url(&url).unwrap();
}
