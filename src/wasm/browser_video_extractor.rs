use gloo::net::http::Request;
use js_sys::Array;
use regex::Regex;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::JsFuture;
use web_sys::{window, console, HtmlIFrameElement};

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VideoInfo {
    pub title: String,
    pub duration: String,
    pub thumbnail: String,
    pub qualities: Vec<VideoQuality>,
    pub video_id: String,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VideoQuality {
    pub quality: String,
    pub size: String,
    pub format: String,
    pub download_url: String,
    pub width: u32,
    pub height: u32,
}

pub struct BrowserVideoExtractor {
    proxy_services: Vec<String>,
    user_agents: Vec<String>,
}

impl BrowserVideoExtractor {
    pub fn new() -> Self {
        Self {
            proxy_services: vec![
                "https://proxy.cors.sh/".to_string(),           // Put the working one first!
                "https://corsproxy.io/?".to_string(),
                "https://cors.bridged.cc/".to_string(),
                "https://api.codetabs.com/v1/proxy?quest=".to_string(),
                "https://cors-proxy.htmldriven.com/?url=".to_string(),
            ],
            user_agents: vec![
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string(),
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string(),
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string(),
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0".to_string(),
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0".to_string(),
            ],
        }
    }

    /// Extract video info using browser simulation with proxy fallback
    pub async fn extract_video_info(&mut self, url: &str) -> Result<VideoInfo, String> {
        console::log_1(&format!("🔍 Starting video extraction for: {}", url).into());

        // Validate Facebook URL
        if !self.is_valid_facebook_url(url) {
            console::log_1(&"❌ Invalid Facebook URL format".into());
            return Err("Invalid Facebook video URL".to_string());
        }

        // Extract video ID
        let video_id = self.extract_video_id(url)?;
        console::log_1(&format!("✅ Extracted video ID: {}", video_id).into());

        // Method 1: Try client-side WASM+JS proxy first (most innovative!)
        console::log_1(&"🚀 Trying client-side WASM+JS proxy (innovative approach)...".into());
        match self.extract_via_client_side_proxy(url).await {
            Ok(info) => {
                console::log_1(&format!("✅ Client-side proxy succeeded! Found {} qualities", info.qualities.len()).into());
                return Ok(info);
            },
            Err(client_proxy_error) => {
                console::log_1(&format!("⚠️ Client-side proxy failed: {}", client_proxy_error).into());

                // Method 2: Fall back to external proxy services
                console::log_1(&"🌐 Falling back to external proxy services...".into());
                match self.extract_via_proxy_services(url).await {
                    Ok(info) => {
                        console::log_1(&format!("✅ External proxy succeeded! Found {} qualities", info.qualities.len()).into());
                        return Ok(info);
                    },
                    Err(proxy_error) => {
                        console::log_1(&format!("⚠️ External proxy services failed: {}", proxy_error).into());

                        // Method 3: Final fallback to browser simulation
                        console::log_1(&"🔄 Final fallback to browser simulation...".into());
                        match self.extract_via_browser_simulation(url).await {
                            Ok(info) => {
                                console::log_1(&format!("✅ Browser simulation succeeded! Found {} qualities", info.qualities.len()).into());
                                Ok(info)
                            },
                            Err(browser_error) => {
                                console::log_1(&format!("❌ All methods failed. Client proxy: {}, External proxy: {}, Browser: {}", client_proxy_error, proxy_error, browser_error).into());
                                Err(format!("All extraction methods failed. Client-side proxy: {}. External proxy: {}. Browser simulation: {}", client_proxy_error, proxy_error, browser_error))
                            }
                        }
                    }
                }
            }
        }
    }

    fn is_valid_facebook_url(&self, url: &str) -> bool {
        // More comprehensive Facebook URL patterns
        let facebook_patterns = [
            // Standard watch URLs with query parameters
            r"facebook\.com/watch\?.*v=\d+",
            r"www\.facebook\.com/watch\?.*v=\d+",
            r"m\.facebook\.com/watch\?.*v=\d+",
            r"fb\.com/watch\?.*v=\d+",

            // Watch URLs with path
            r"facebook\.com/watch/.*",
            r"www\.facebook\.com/watch/.*",
            r"m\.facebook\.com/watch/.*",
            r"fb\.com/watch/.*",

            // Video URLs in user profiles
            r"facebook\.com/.*/videos/\d+",
            r"www\.facebook\.com/.*/videos/\d+",
            r"m\.facebook\.com/.*/videos/\d+",
            r"fb\.com/.*/videos/\d+",

            // Reel URLs
            r"facebook\.com/reel/\d+",
            r"www\.facebook\.com/reel/\d+",
            r"m\.facebook\.com/reel/\d+",
            r"fb\.com/reel/\d+",

            // Post URLs that might contain videos
            r"facebook\.com/.*/posts/\d+",
            r"www\.facebook\.com/.*/posts/\d+",
            r"m\.facebook\.com/.*/posts/\d+",
            r"fb\.com/.*/posts/\d+",
        ];

        facebook_patterns
            .iter()
            .any(|pattern| {
                if let Ok(regex) = Regex::new(pattern) {
                    regex.is_match(url)
                } else {
                    false
                }
            })
    }

    /// Client-side WASM+JS proxy - acts like a local proxy server in the browser
    async fn extract_via_client_side_proxy(&self, url: &str) -> Result<VideoInfo, String> {
        console::log_1(&"🚀 Initializing client-side WASM+JS proxy...".into());

        // Use WASM + JS to create a client-side proxy that bypasses CORS
        match self.wasm_js_cors_bypass(url).await {
            Ok(response_data) => {
                console::log_1(&format!("✅ WASM+JS proxy fetched {} bytes", response_data.len()).into());

                // Parse the response using WASM
                self.parse_facebook_response_with_wasm(&response_data, url).await
            },
            Err(e) => {
                console::log_1(&format!("❌ WASM+JS proxy failed: {}", e).into());
                Err(format!("Client-side proxy failed: {}", e))
            }
        }
    }

    /// WASM+JS CORS bypass technique using multiple advanced methods
    async fn wasm_js_cors_bypass(&self, url: &str) -> Result<Vec<u8>, String> {
        console::log_1(&"🔄 Using advanced WASM+JS CORS bypass techniques...".into());

        // Method 1: Try Service Worker proxy approach
        console::log_1(&"🔧 Method 1: Trying Service Worker proxy...".into());
        match self.try_service_worker_proxy(url).await {
            Ok(data) => {
                console::log_1(&"✅ Service Worker proxy succeeded!".into());
                return Ok(data);
            },
            Err(e) => console::log_1(&format!("❌ Service Worker proxy failed: {}", e).into())
        }

        // Method 2: Try WebAssembly fetch with custom headers
        console::log_1(&"🚀 Method 2: Trying WASM fetch with custom headers...".into());
        match self.try_wasm_fetch_with_headers(url).await {
            Ok(data) => {
                console::log_1(&"✅ WASM fetch succeeded!".into());
                return Ok(data);
            },
            Err(e) => console::log_1(&format!("❌ WASM fetch failed: {}", e).into())
        }

        // Method 3: Try iframe with enhanced techniques
        console::log_1(&"📱 Method 3: Trying enhanced iframe techniques...".into());
        match self.try_enhanced_iframe_proxy(url).await {
            Ok(data) => {
                console::log_1(&"✅ Enhanced iframe proxy succeeded!".into());
                return Ok(data);
            },
            Err(e) => console::log_1(&format!("❌ Enhanced iframe proxy failed: {}", e).into())
        }

        // Method 4: Try WebRTC data channel approach
        console::log_1(&"📡 Method 4: Trying WebRTC data channel...".into());
        match self.try_webrtc_data_channel(url).await {
            Ok(data) => {
                console::log_1(&"✅ WebRTC data channel succeeded!".into());
                return Ok(data);
            },
            Err(e) => console::log_1(&format!("❌ WebRTC data channel failed: {}", e).into())
        }

        Err("All advanced WASM+JS CORS bypass methods failed".to_string())
    }

    /// Try Service Worker proxy approach
    async fn try_service_worker_proxy(&self, url: &str) -> Result<Vec<u8>, String> {
        console::log_1(&"🔧 Setting up Service Worker proxy...".into());

        let window = window().unwrap();
        let navigator = window.navigator();

        // Check if service workers are supported
        if let Ok(service_worker) = js_sys::Reflect::get(&navigator, &"serviceWorker".into()) {
            if !service_worker.is_undefined() {
                // Create a service worker that acts as a proxy
                let sw_code = format!(r#"
                    self.addEventListener('fetch', event => {{
                        const url = new URL(event.request.url);

                        // Intercept requests to our proxy endpoint
                        if (url.pathname === '/wasm-proxy') {{
                            const target = url.searchParams.get('target');
                            if (target) {{
                                console.log('🔄 SW Proxy intercepting:', target);

                                event.respondWith(
                                    fetch(target, {{
                                        method: 'GET',
                                        mode: 'no-cors',
                                        credentials: 'omit',
                                        headers: {{
                                            'User-Agent': 'Mozilla/5.0 (compatible; WASM-Proxy/1.0)',
                                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                                            'Accept-Language': 'en-US,en;q=0.5',
                                            'Referer': 'https://www.facebook.com/',
                                            'Cache-Control': 'no-cache'
                                        }}
                                    }}).then(response => {{
                                        console.log('✅ SW Proxy response:', response.status);
                                        return response;
                                    }}).catch(error => {{
                                        console.log('❌ SW Proxy error:', error);
                                        return new Response('SW Proxy error', {{ status: 500 }});
                                    }})
                                );
                            }}
                        }}
                    }});
                "#);

                // Register the service worker
                if let Ok(_) = self.register_service_worker(&sw_code).await {
                    // Wait for service worker to be ready
                    gloo::timers::future::TimeoutFuture::new(2000).await;

                    // Make request through service worker
                    let proxy_url = format!("/wasm-proxy?target={}", js_sys::encode_uri_component(url));
                    return self.fetch_through_service_worker(&proxy_url).await;
                }
            }
        }

        Err("Service Worker not supported or registration failed".to_string())
    }

    /// Try WASM fetch with custom headers
    async fn try_wasm_fetch_with_headers(&self, url: &str) -> Result<Vec<u8>, String> {
        console::log_1(&"🚀 Using WASM fetch with spoofed headers...".into());

        // Create request with headers that mimic a real browser
        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_mode(web_sys::RequestMode::NoCors); // Use no-cors mode
        opts.set_credentials(web_sys::RequestCredentials::Omit);
        opts.set_cache(web_sys::RequestCache::NoCache);

        let request = web_sys::Request::new_with_str_and_init(url, &opts)
            .map_err(|e| format!("Failed to create WASM request: {:?}", e))?;

        // Add headers that make it look like a real browser request
        request.headers().set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8").ok();
        request.headers().set("Accept-Language", "en-US,en;q=0.5").ok();
        request.headers().set("Accept-Encoding", "gzip, deflate, br").ok();
        request.headers().set("DNT", "1").ok();
        request.headers().set("Connection", "keep-alive").ok();
        request.headers().set("Upgrade-Insecure-Requests", "1").ok();
        request.headers().set("Sec-Fetch-Dest", "document").ok();
        request.headers().set("Sec-Fetch-Mode", "navigate").ok();
        request.headers().set("Sec-Fetch-Site", "none").ok();
        request.headers().set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0").ok();

        console::log_1(&"📤 Sending WASM request with spoofed headers...".into());
        let window = window().unwrap();
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|e| format!("WASM fetch failed: {:?}", e))?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();
        console::log_1(&format!("📊 WASM fetch response status: {}", resp.status()).into());

        if resp.ok() || resp.type_() == web_sys::ResponseType::Opaque {
            let array_buffer = JsFuture::from(resp.array_buffer().unwrap()).await
                .map_err(|e| format!("Failed to get WASM response data: {:?}", e))?;

            let uint8_array = js_sys::Uint8Array::new(&array_buffer);
            let mut data = vec![0; uint8_array.length() as usize];
            uint8_array.copy_to(&mut data);

            console::log_1(&format!("✅ WASM fetch returned {} bytes", data.len()).into());
            Ok(data)
        } else {
            Err(format!("WASM fetch failed with status: {}", resp.status()))
        }
    }

    /// Try enhanced iframe proxy with multiple techniques
    async fn try_enhanced_iframe_proxy(&self, url: &str) -> Result<Vec<u8>, String> {
        console::log_1(&"📱 Using enhanced iframe proxy techniques...".into());

        let window = window().unwrap();
        let document = window.document().unwrap();

        // Try different iframe configurations
        let iframe_configs = vec![
            ("allow-scripts allow-same-origin allow-forms", ""),
            ("allow-scripts allow-same-origin", "referrerpolicy='no-referrer'"),
            ("", "loading='eager'"),
        ];

        for (i, (sandbox, extra_attrs)) in iframe_configs.iter().enumerate() {
            console::log_1(&format!("🔄 Trying iframe config {}/{}", i + 1, iframe_configs.len()).into());

            let iframe = document.create_element("iframe")
                .map_err(|e| format!("Failed to create iframe: {:?}", e))?;

            iframe.set_attribute("src", url).ok();
            iframe.set_attribute("style", "width: 100px; height: 100px; position: absolute; left: -9999px; opacity: 0;").ok();

            if !sandbox.is_empty() {
                iframe.set_attribute("sandbox", sandbox).ok();
            }
            if !extra_attrs.is_empty() {
                for attr in extra_attrs.split(' ') {
                    if let Some((key, value)) = attr.split_once('=') {
                        iframe.set_attribute(key, &value.trim_matches('\'')).ok();
                    }
                }
            }

            document.body().unwrap().append_child(&iframe).ok();

            // Wait longer for this attempt
            gloo::timers::future::TimeoutFuture::new(8000).await;

            // Try to extract content
            if let Ok(data) = self.extract_content_from_iframe_with_wasm(&iframe).await {
                let _ = document.body().unwrap().remove_child(&iframe);
                return Ok(data);
            }

            let _ = document.body().unwrap().remove_child(&iframe);
        }

        Err("All enhanced iframe configurations failed".to_string())
    }

    /// Try WebRTC data channel approach (experimental)
    async fn try_webrtc_data_channel(&self, url: &str) -> Result<Vec<u8>, String> {
        console::log_1(&"📡 Trying WebRTC data channel approach...".into());

        // This is an experimental approach using WebRTC for data transfer
        // For now, we'll simulate it and return an error to continue to other methods
        gloo::timers::future::TimeoutFuture::new(1000).await;

        Err("WebRTC data channel not implemented yet".to_string())
    }

    /// Register a service worker for proxying
    async fn register_service_worker(&self, sw_code: &str) -> Result<(), String> {
        console::log_1(&"📝 Registering Service Worker proxy...".into());

        let window = window().unwrap();
        let navigator = window.navigator();

        if let Ok(service_worker) = js_sys::Reflect::get(&navigator, &"serviceWorker".into()) {
            // Create blob URL for service worker
            let blob_parts = js_sys::Array::new();
            blob_parts.push(&sw_code.into());

            let blob_options = web_sys::BlobPropertyBag::new();
            blob_options.set_type("application/javascript");

            let blob = web_sys::Blob::new_with_str_sequence_and_options(&blob_parts, &blob_options)
                .map_err(|e| format!("Failed to create SW blob: {:?}", e))?;

            let blob_url = web_sys::Url::create_object_url_with_blob(&blob)
                .map_err(|e| format!("Failed to create SW blob URL: {:?}", e))?;

            // Register service worker
            let register_promise = js_sys::Reflect::get(&service_worker, &"register".into())
                .map_err(|_| "Failed to get register method")?;

            if let Ok(register_fn) = register_promise.dyn_into::<js_sys::Function>() {
                let args = js_sys::Array::new();
                args.push(&blob_url.into());

                let _result = register_fn.apply(&service_worker, &args)
                    .map_err(|e| format!("Failed to register SW: {:?}", e))?;

                console::log_1(&"✅ Service Worker registered successfully!".into());

                // Clean up blob URL
                web_sys::Url::revoke_object_url(&blob_url).ok();

                Ok(())
            } else {
                Err("Service worker register is not a function".to_string())
            }
        } else {
            Err("Service Worker not available".to_string())
        }
    }

    /// Fetch through service worker
    async fn fetch_through_service_worker(&self, proxy_url: &str) -> Result<Vec<u8>, String> {
        console::log_1(&format!("📡 Fetching through Service Worker: {}", proxy_url).into());

        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_mode(web_sys::RequestMode::SameOrigin);
        opts.set_credentials(web_sys::RequestCredentials::Include);

        let request = web_sys::Request::new_with_str_and_init(proxy_url, &opts)
            .map_err(|e| format!("Failed to create SW request: {:?}", e))?;

        let window = window().unwrap();
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|e| format!("SW fetch failed: {:?}", e))?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();
        console::log_1(&format!("📊 SW response status: {}", resp.status()).into());

        if resp.ok() {
            let array_buffer = JsFuture::from(resp.array_buffer().unwrap()).await
                .map_err(|e| format!("Failed to get SW response data: {:?}", e))?;

            let uint8_array = js_sys::Uint8Array::new(&array_buffer);
            let mut data = vec![0; uint8_array.length() as usize];
            uint8_array.copy_to(&mut data);

            console::log_1(&format!("✅ SW returned {} bytes", data.len()).into());
            Ok(data)
        } else {
            Err(format!("SW request failed with status: {}", resp.status()))
        }
    }

    /// Extract content from iframe using WASM processing
    async fn extract_content_from_iframe_with_wasm(&self, iframe: &web_sys::Element) -> Result<Vec<u8>, String> {
        console::log_1(&"🔍 Extracting content from iframe with WASM...".into());

        if let Ok(iframe_element) = iframe.clone().dyn_into::<web_sys::HtmlIFrameElement>() {
            // Try to access iframe content
            if let Some(content_document) = iframe_element.content_document() {
                console::log_1(&"✅ Successfully accessed iframe content document".into());

                if let Some(html_element) = content_document.document_element() {
                    let html_content = html_element.outer_html();
                    console::log_1(&format!("📄 Extracted {} characters from iframe", html_content.len()).into());

                    // Check if we got meaningful content
                    if html_content.len() > 1000 && html_content.contains("facebook") {
                        return Ok(html_content.into_bytes());
                    } else {
                        console::log_1(&"⚠️ Iframe content seems incomplete or blocked".into());
                    }
                }
            } else {
                console::log_1(&"❌ Cannot access iframe content document (CORS blocked)".into());

                // Try alternative approach: inject script into iframe
                return self.try_iframe_script_injection(&iframe_element).await;
            }
        }

        Err("Failed to extract meaningful content from iframe".to_string())
    }

    /// Try to inject script into iframe to extract video data
    async fn try_iframe_script_injection(&self, iframe: &web_sys::HtmlIFrameElement) -> Result<Vec<u8>, String> {
        console::log_1(&"💉 Trying iframe script injection approach...".into());

        // Create a script that tries to communicate with the iframe
        let script_content = r#"
            (function() {
                try {
                    // Try to send a message to the iframe
                    const iframe = document.querySelector('iframe[src*="facebook.com"]');
                    if (iframe && iframe.contentWindow) {
                        iframe.contentWindow.postMessage({
                            type: 'EXTRACT_VIDEO_DATA',
                            origin: window.location.origin
                        }, '*');

                        // Listen for response
                        window.addEventListener('message', function(event) {
                            if (event.data && event.data.type === 'VIDEO_DATA_RESPONSE') {
                                window.wasmVideoData = event.data.content;
                            }
                        });
                    }
                } catch(e) {
                    console.log('Script injection error:', e);
                }
            })();
        "#;

        let window = window().unwrap();
        let document = window.document().unwrap();

        let script = document.create_element("script")
            .map_err(|e| format!("Failed to create script: {:?}", e))?;
        script.set_text_content(Some(script_content));
        document.head().unwrap().append_child(&script)
            .map_err(|e| format!("Failed to append script: {:?}", e))?;

        // Wait for potential response
        gloo::timers::future::TimeoutFuture::new(3000).await;

        // Try to get result
        let result = if let Ok(result_value) = js_sys::Reflect::get(&window, &"wasmVideoData".into()) {
            if !result_value.is_undefined() && !result_value.is_null() {
                if let Some(result_str) = result_value.as_string() {
                    console::log_1(&format!("✅ Got video data via script injection: {} chars", result_str.len()).into());
                    Ok(result_str.into_bytes())
                } else {
                    Err("Invalid result type from script injection".to_string())
                }
            } else {
                Err("No result from script injection".to_string())
            }
        } else {
            Err("Failed to get script injection result".to_string())
        };

        // Cleanup
        let _ = document.head().unwrap().remove_child(&script);

        result
    }

    /// Parse Facebook response using WASM processing
    async fn parse_facebook_response_with_wasm(&self, response_data: &[u8], original_url: &str) -> Result<VideoInfo, String> {
        console::log_1(&format!("🔍 Parsing {} bytes with WASM...", response_data.len()).into());

        // Convert bytes to string
        let response_text = String::from_utf8_lossy(response_data);
        console::log_1(&format!("📄 Response text length: {} characters", response_text.len()).into());

        // Use WASM to parse Facebook's response for video URLs
        if let Ok(video_info) = self.extract_video_urls_from_html_content(&response_text, &self.extract_video_id(original_url)?) {
            console::log_1(&format!("✅ WASM parsing found {} video qualities", video_info.qualities.len()).into());
            Ok(video_info)
        } else {
            console::log_1(&"❌ WASM parsing failed to find video URLs".into());
            Err("Failed to extract video URLs from Facebook response".to_string())
        }
    }





    /// Browser simulation approach - uses local processing without external requests
    async fn extract_via_browser_simulation(&self, url: &str) -> Result<VideoInfo, String> {
        console::log_1(&"🎯 Starting browser simulation methods...".into());

        // Step 1: Try local video data extraction (no external requests)
        console::log_1(&"📱 Step 1: Trying local video analysis...".into());
        match self.extract_via_local_video_analysis(url).await {
            Ok(info) => {
                console::log_1(&"✅ Local video analysis succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ Local video analysis failed: {}", e).into())
        }

        // Step 2: Try browser storage analysis
        console::log_1(&"💾 Step 2: Trying browser storage analysis...".into());
        match self.extract_via_browser_storage_analysis(url).await {
            Ok(info) => {
                console::log_1(&"✅ Browser storage analysis succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ Browser storage analysis failed: {}", e).into())
        }

        // Step 3: Try URL pattern analysis with real data extraction
        console::log_1(&"🔍 Step 3: Trying URL pattern analysis...".into());
        match self.extract_via_url_pattern_analysis(url).await {
            Ok(info) => {
                console::log_1(&"✅ URL pattern analysis succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ URL pattern analysis failed: {}", e).into())
        }

        // Step 4: Try window object analysis
        console::log_1(&"🪟 Step 4: Trying window object analysis...".into());
        match self.extract_via_window_object_analysis(url).await {
            Ok(info) => {
                console::log_1(&"✅ Window object analysis succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ Window object analysis failed: {}", e).into())
        }

        console::log_1(&"❌ All browser simulation methods failed".into());
        Err("All browser simulation methods failed".to_string())
    }

    async fn extract_via_local_video_analysis(&self, url: &str) -> Result<VideoInfo, String> {
        let video_id = self.extract_video_id(url)?;

        // Try to extract from current page context if we're on Facebook
        let window = window().unwrap();
        let location = window.location();
        let current_url = location.href().unwrap_or_default();

        if current_url.contains("facebook.com") {
            // We're already on Facebook, try to extract from current page
            if let Ok(info) = self.extract_from_current_facebook_page(&video_id).await {
                return Ok(info);
            }
        }

        // Try to extract using browser's built-in video detection
        if let Ok(info) = self.extract_via_media_detection(&video_id).await {
            return Ok(info);
        }

        Err("Local video analysis failed".to_string())
    }

    async fn extract_from_current_facebook_page(&self, video_id: &str) -> Result<VideoInfo, String> {
        let window = window().unwrap();
        let document = window.document().unwrap();

        // Look for video elements in the current page
        if let Ok(videos) = document.query_selector_all("video") {
            for i in 0..videos.length() {
                if let Some(video) = videos.get(i) {
                    if let Ok(element) = video.dyn_into::<web_sys::Element>() {
                        if let Some(src) = element.get_attribute("src") {
                            if src.contains("fbcdn.net") {
                                return self.create_video_info_from_real_url(&src, video_id);
                            }
                        }
                    }
                }
            }
        }

        // Look for data in script tags
        if let Ok(scripts) = document.query_selector_all("script") {
            for i in 0..scripts.length() {
                if let Some(script) = scripts.get(i) {
                    if let Ok(element) = script.dyn_into::<web_sys::Element>() {
                        let content = element.text_content().unwrap_or_default();
                        if content.contains("fbcdn.net") && content.contains("mp4") {
                            if let Ok(info) = self.extract_video_urls_from_html_content(&content, video_id) {
                                return Ok(info);
                            }
                        }
                    }
                }
            }
        }

        Err("No video data found in current Facebook page".to_string())
    }

    async fn extract_via_media_detection(&self, video_id: &str) -> Result<VideoInfo, String> {
        let window = window().unwrap();

        // Try to access media devices and streams (if available)
        if let Ok(navigator) = js_sys::Reflect::get(&window, &JsValue::from_str("navigator")) {
            if let Ok(media_devices) = js_sys::Reflect::get(&navigator, &JsValue::from_str("mediaDevices")) {
                if !media_devices.is_undefined() {
                    // Media API is available, try to detect video streams
                    return self.analyze_media_streams(video_id).await;
                }
            }
        }

        Err("Media detection not available".to_string())
    }

    async fn analyze_media_streams(&self, video_id: &str) -> Result<VideoInfo, String> {
        // This is a placeholder for media stream analysis
        // In practice, this would analyze any active media streams
        // For now, we'll return an error to continue to other methods
        Err("No active media streams detected".to_string())
    }

    async fn extract_via_browser_storage_analysis(&self, url: &str) -> Result<VideoInfo, String> {
        let video_id = self.extract_video_id(url)?;
        let window = window().unwrap();

        // Check localStorage for Facebook video data
        if let Some(storage) = window.local_storage().ok().flatten() {
            for i in 0..storage.length().unwrap_or(0) {
                if let Ok(Some(key)) = storage.key(i) {
                    if key.contains("video") || key.contains("facebook") || key.contains("fb") {
                        if let Ok(Some(value)) = storage.get_item(&key) {
                            if value.contains("fbcdn.net") && value.contains(&video_id) {
                                if let Ok(info) = self.extract_video_urls_from_html_content(&value, &video_id) {
                                    return Ok(info);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Check sessionStorage
        if let Some(storage) = window.session_storage().ok().flatten() {
            for i in 0..storage.length().unwrap_or(0) {
                if let Ok(Some(key)) = storage.key(i) {
                    if key.contains("video") || key.contains("facebook") || key.contains("fb") {
                        if let Ok(Some(value)) = storage.get_item(&key) {
                            if value.contains("fbcdn.net") && value.contains(&video_id) {
                                if let Ok(info) = self.extract_video_urls_from_html_content(&value, &video_id) {
                                    return Ok(info);
                                }
                            }
                        }
                    }
                }
            }
        }

        Err("No video data found in browser storage".to_string())
    }

    async fn extract_via_window_object_analysis(&self, url: &str) -> Result<VideoInfo, String> {
        let video_id = self.extract_video_id(url)?;
        let window = window().unwrap();

        // Check for Facebook-specific global variables
        let fb_globals = [
            "FB", "facebook", "_fb", "fbAsyncInit", "FB_PUBLIC_LOAD_DATA_",
            "require", "__d", "__DEV__", "BootloaderConfig"
        ];

        for global_name in &fb_globals {
            if let Ok(global_obj) = js_sys::Reflect::get(&window, &JsValue::from_str(global_name)) {
                if !global_obj.is_undefined() {
                    // Try to extract video data from this global object
                    if let Ok(info) = self.analyze_global_object(&global_obj, &video_id).await {
                        return Ok(info);
                    }
                }
            }
        }

        // Check for any objects that might contain video data
        let keys = js_sys::Object::keys(&window);
        for i in 0..keys.length() {
            if let Some(key_str) = keys.get(i).as_string() {
                if key_str.to_lowercase().contains("video") || key_str.to_lowercase().contains("fb") {
                    if let Ok(obj) = js_sys::Reflect::get(&window, &JsValue::from_str(&key_str)) {
                        if let Ok(info) = self.analyze_global_object(&obj, &video_id).await {
                            return Ok(info);
                        }
                    }
                }
            }
        }

        Err("No video data found in window objects".to_string())
    }

    async fn analyze_global_object(&self, obj: &JsValue, video_id: &str) -> Result<VideoInfo, String> {
        // Convert object to string and search for video URLs
        let obj_str = format!("{:?}", obj);
        if obj_str.contains("fbcdn.net") && obj_str.contains("mp4") {
            if let Ok(info) = self.extract_video_urls_from_html_content(&obj_str, video_id) {
                return Ok(info);
            }
        }

        // Try to access object properties if it's an object
        if obj.is_object() {
            if let Some(obj_ref) = obj.dyn_ref::<js_sys::Object>() {
                let keys = js_sys::Object::keys(obj_ref);
                for i in 0..keys.length() {
                    if let Some(key_str) = keys.get(i).as_string() {
                        if let Ok(value) = js_sys::Reflect::get(&obj, &JsValue::from_str(&key_str)) {
                            if let Some(value_str) = value.as_string() {
                                if value_str.contains("fbcdn.net") && value_str.contains("mp4") {
                                    if let Ok(info) = self.extract_video_urls_from_html_content(&value_str, video_id) {
                                        return Ok(info);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Err("No video data found in global object".to_string())
    }



    async fn extract_via_url_pattern_analysis(&self, url: &str) -> Result<VideoInfo, String> {
        console::log_1(&"🔍 Starting URL pattern analysis...".into());

        // Extract real video information using browser DOM manipulation
        let video_id = self.extract_video_id(url)?;
        console::log_1(&format!("🆔 Working with video ID: {}", video_id).into());

        // Try to extract real video data using browser-native methods
        console::log_1(&"🌐 Method 1: Trying DOM-based extraction...".into());
        match self.extract_real_video_data_via_dom(url, &video_id).await {
            Ok(info) => {
                console::log_1(&"✅ DOM extraction succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ DOM extraction failed: {}", e).into())
        }

        // Try alternative real extraction methods
        console::log_1(&"📄 Method 2: Trying page source extraction...".into());
        match self.extract_real_video_data_via_page_source(url, &video_id).await {
            Ok(info) => {
                console::log_1(&"✅ Page source extraction succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ Page source extraction failed: {}", e).into())
        }

        // Try extracting from Facebook's mobile page
        console::log_1(&"📱 Method 3: Trying mobile page extraction...".into());
        match self.extract_real_video_data_via_mobile(url, &video_id).await {
            Ok(info) => {
                console::log_1(&"✅ Mobile extraction succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ Mobile extraction failed: {}", e).into())
        }

        // Last resort: Try to find real video URLs through network inspection
        console::log_1(&"🕸️ Method 4: Trying network analysis...".into());
        match self.extract_real_video_data_via_network_analysis(url, &video_id).await {
            Ok(info) => {
                console::log_1(&"✅ Network analysis succeeded!".into());
                return Ok(info);
            },
            Err(e) => console::log_1(&format!("❌ Network analysis failed: {}", e).into())
        }

        console::log_1(&"❌ All URL pattern analysis methods failed".into());
        Err("Could not extract real video data".to_string())
    }

    async fn extract_real_video_data_via_dom(&self, url: &str, video_id: &str) -> Result<VideoInfo, String> {
        let window = window().unwrap();
        let document = window.document().unwrap();

        // Create a hidden iframe that loads the Facebook page
        let iframe = document.create_element("iframe").map_err(|_| "Failed to create iframe")?;
        iframe.set_attribute("src", url).map_err(|_| "Failed to set iframe src")?;
        iframe.set_attribute("style", "width: 1px; height: 1px; position: absolute; left: -9999px; opacity: 0; pointer-events: none;").map_err(|_| "Failed to set iframe style")?;
        iframe.set_attribute("sandbox", "allow-scripts allow-same-origin").map_err(|_| "Failed to set sandbox")?;

        document.body().unwrap().append_child(&iframe).map_err(|_| "Failed to append iframe")?;

        // Wait for page to load
        gloo::timers::future::TimeoutFuture::new(3000).await;

        // Try to extract video data from the iframe
        let result = self.extract_video_data_from_iframe(&iframe, video_id).await;

        // Cleanup
        let _ = document.body().unwrap().remove_child(&iframe);

        result
    }

    async fn extract_video_data_from_iframe(&self, iframe: &web_sys::Element, video_id: &str) -> Result<VideoInfo, String> {
        // Try to access iframe content and extract video URLs
        if let Ok(iframe_element) = iframe.clone().dyn_into::<web_sys::HtmlIFrameElement>() {
            if let Some(content_document) = iframe_element.content_document() {
                // Look for video elements
                let video_elements = content_document.query_selector_all("video").ok();
                if let Some(videos) = video_elements {
                    for i in 0..videos.length() {
                        if let Some(video) = videos.get(i) {
                            if let Ok(element) = video.dyn_into::<web_sys::Element>() {
                                if let Some(src) = element.get_attribute("src") {
                                    if !src.is_empty() && src.contains("fbcdn.net") {
                                        return self.create_video_info_from_real_url(&src, video_id);
                                    }
                                }
                            }
                        }
                    }
                }

                // Look for source elements
                let source_elements = content_document.query_selector_all("source").ok();
                if let Some(sources) = source_elements {
                    for i in 0..sources.length() {
                        if let Some(source) = sources.get(i) {
                            if let Ok(element) = source.dyn_into::<web_sys::Element>() {
                                if let Some(src) = element.get_attribute("src") {
                                    if !src.is_empty() && src.contains("fbcdn.net") {
                                        return self.create_video_info_from_real_url(&src, video_id);
                                    }
                                }
                            }
                        }
                    }
                }

                // Look for data attributes that might contain video URLs
                if let Some(html_element) = content_document.document_element() {
                    let html_content = html_element.inner_html();
                    if let Ok(info) = self.extract_video_urls_from_html_content(&html_content, video_id) {
                        return Ok(info);
                    }
                }
            }
        }

        Err("Could not extract video data from iframe".to_string())
    }

    async fn extract_real_video_data_via_page_source(&self, url: &str, video_id: &str) -> Result<VideoInfo, String> {
        // Try to get page source using a different approach
        let window = window().unwrap();

        // Create a script that tries to fetch the page and extract video URLs
        let script_content = format!(r#"
            (async function() {{
                try {{
                    const response = await fetch('{}', {{
                        method: 'GET',
                        mode: 'no-cors',
                        credentials: 'include'
                    }});

                    // Even if we can't read the response due to no-cors,
                    // the browser might cache it and we can try to access it differently
                    window.facebookPageLoaded = true;
                }} catch(e) {{
                    console.log('Page fetch attempt:', e);
                }}
            }})();
        "#, url);

        let document = window.document().unwrap();
        let script = document.create_element("script").map_err(|_| "Failed to create script")?;
        script.set_text_content(Some(&script_content));
        document.head().unwrap().append_child(&script).map_err(|_| "Failed to append script")?;

        // Wait for potential loading
        gloo::timers::future::TimeoutFuture::new(2000).await;

        // Try to extract from browser cache or network requests
        if let Ok(info) = self.extract_from_browser_cache(video_id).await {
            let _ = document.head().unwrap().remove_child(&script);
            return Ok(info);
        }

        let _ = document.head().unwrap().remove_child(&script);
        Err("Could not extract from page source".to_string())
    }

    async fn extract_real_video_data_via_mobile(&self, url: &str, video_id: &str) -> Result<VideoInfo, String> {
        // Try mobile version which might have different CORS policies
        let mobile_url = url.replace("www.facebook.com", "m.facebook.com");

        // Use a different approach for mobile
        let window = window().unwrap();
        let document = window.document().unwrap();

        // Create a link element and try to preload the mobile page
        let link = document.create_element("link").map_err(|_| "Failed to create link")?;
        link.set_attribute("rel", "prefetch").map_err(|_| "Failed to set rel")?;
        link.set_attribute("href", &mobile_url).map_err(|_| "Failed to set href")?;
        document.head().unwrap().append_child(&link).map_err(|_| "Failed to append link")?;

        // Wait for prefetch
        gloo::timers::future::TimeoutFuture::new(1500).await;

        // Try to extract from prefetched content
        if let Ok(info) = self.extract_from_prefetched_content(video_id).await {
            let _ = document.head().unwrap().remove_child(&link);
            return Ok(info);
        }

        let _ = document.head().unwrap().remove_child(&link);
        Err("Could not extract from mobile version".to_string())
    }

    async fn extract_real_video_data_via_network_analysis(&self, url: &str, video_id: &str) -> Result<VideoInfo, String> {
        // Try to analyze network requests that might contain video URLs
        let window = window().unwrap();

        // Monitor for any network requests that might contain video data
        let script_content = format!(r#"
            (function() {{
                const originalFetch = window.fetch;
                window.facebookVideoUrls = [];

                window.fetch = function(...args) {{
                    const url = args[0];
                    if (typeof url === 'string' && (url.includes('fbcdn.net') || url.includes('facebook.com'))) {{
                        window.facebookVideoUrls.push(url);
                    }}
                    return originalFetch.apply(this, args);
                }};

                // Try to trigger some network requests
                const img = new Image();
                img.src = '{}';
            }})();
        "#, url);

        let document = window.document().unwrap();
        let script = document.create_element("script").map_err(|_| "Failed to create script")?;
        script.set_text_content(Some(&script_content));
        document.head().unwrap().append_child(&script).map_err(|_| "Failed to append script")?;

        // Wait for potential network activity
        gloo::timers::future::TimeoutFuture::new(2000).await;

        // Check if any video URLs were captured
        if let Ok(urls) = js_sys::Reflect::get(&window, &JsValue::from_str("facebookVideoUrls")) {
            if let Some(urls_array) = urls.dyn_ref::<js_sys::Array>() {
                for i in 0..urls_array.length() {
                    if let Some(url_val) = urls_array.get(i).as_string() {
                        if url_val.contains("video") && url_val.contains("fbcdn.net") {
                            let _ = document.head().unwrap().remove_child(&script);
                            return self.create_video_info_from_real_url(&url_val, video_id);
                        }
                    }
                }
            }
        }

        let _ = document.head().unwrap().remove_child(&script);
        Err("Could not extract from network analysis".to_string())
    }

    fn create_video_info_from_real_url(&self, video_url: &str, video_id: &str) -> Result<VideoInfo, String> {
        console::log_1(&format!("🎬 Creating video info from real URL: {}", video_url).into());

        // Analyze the URL to extract quality information
        let (quality_name, width, height) = self.analyze_facebook_video_url(video_url);
        console::log_1(&format!("📊 Detected quality: {} ({}x{})", quality_name, width, height).into());

        // Create video info from a real Facebook video URL
        let qualities = vec![
            VideoQuality {
                quality: quality_name,
                size: "Unknown".to_string(),
                format: "MP4".to_string(),
                download_url: video_url.to_string(),
                width,
                height,
            }
        ];

        let title = format!("Facebook Video {}", &video_id[..8.min(video_id.len())]);
        console::log_1(&format!("📝 Generated title: {}", title).into());

        let video_info = VideoInfo {
            title,
            duration: "Unknown".to_string(),
            thumbnail: String::new(),
            qualities,
            video_id: video_id.to_string(),
        };

        console::log_1(&"✅ Video info created successfully!".into());
        Ok(video_info)
    }

    fn analyze_facebook_video_url(&self, url: &str) -> (String, u32, u32) {
        // Analyze Facebook video URL to determine quality
        if url.contains("720") || url.contains("hd") {
            ("720p HD".to_string(), 1280, 720)
        } else if url.contains("480") || url.contains("sd") {
            ("480p SD".to_string(), 854, 480)
        } else if url.contains("1080") {
            ("1080p Full HD".to_string(), 1920, 1080)
        } else {
            ("Original".to_string(), 0, 0)
        }
    }

    fn extract_video_urls_from_html_content(&self, html: &str, video_id: &str) -> Result<VideoInfo, String> {
        // Look for video URLs in HTML content using regex patterns
        let video_url_patterns = [
            r#"https://video[^"]*\.fbcdn\.net/[^"]*\.mp4[^"]*"#,
            r#"https://[^"]*\.fbcdn\.net/[^"]*video[^"]*\.mp4[^"]*"#,
            r#""hd_src":"([^"]+)""#,
            r#""sd_src":"([^"]+)""#,
            r#""playable_url":"([^"]+)""#,
        ];

        for pattern in &video_url_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(html) {
                    let url = if captures.len() > 1 {
                        captures.get(1).unwrap().as_str()
                    } else {
                        captures.get(0).unwrap().as_str()
                    };

                    if !url.is_empty() {
                        return self.create_video_info_from_real_url(url, video_id);
                    }
                }
            }
        }

        Err("No video URLs found in HTML content".to_string())
    }

    async fn extract_from_browser_cache(&self, video_id: &str) -> Result<VideoInfo, String> {
        // Try to extract from browser cache or storage
        let window = window().unwrap();

        // Check if there's any cached video data
        if let Some(storage) = window.local_storage().ok().flatten() {
            // Look for any Facebook video related data in localStorage
            for i in 0..storage.length().unwrap_or(0) {
                if let Ok(Some(key)) = storage.key(i) {
                    if key.contains("facebook") || key.contains("video") {
                        if let Ok(Some(value)) = storage.get_item(&key) {
                            if value.contains("fbcdn.net") && value.contains("mp4") {
                                return self.extract_video_urls_from_html_content(&value, video_id);
                            }
                        }
                    }
                }
            }
        }

        Err("No video data found in browser cache".to_string())
    }

    async fn extract_from_prefetched_content(&self, video_id: &str) -> Result<VideoInfo, String> {
        // Try to extract from prefetched content
        let window = window().unwrap();

        // Check if prefetch loaded any useful data
        if let Ok(performance) = js_sys::Reflect::get(&window, &JsValue::from_str("performance")) {
            if let Ok(entries) = js_sys::Reflect::get(&performance, &JsValue::from_str("getEntries")) {
                if let Some(get_entries) = entries.dyn_ref::<js_sys::Function>() {
                    if let Ok(entries_result) = get_entries.call0(&performance) {
                        // Analyze performance entries for video-related resources
                        // This is a simplified approach - in practice, you'd need more sophisticated analysis
                        return Err("No video data found in prefetched content".to_string());
                    }
                }
            }
        }

        Err("Could not access prefetched content".to_string())
    }



    async fn fetch_with_minimal_headers(&self, url: &str) -> Result<String, String> {
        let request = Request::get(url)
            .header("Accept", "application/json,text/html,*/*")
            .header("User-Agent", &self.user_agents[0]);

        match request.send().await {
            Ok(response) => {
                if response.ok() {
                    response.text().await.map_err(|_| "Failed to get response text".to_string())
                } else {
                    Err(format!("HTTP error: {}", response.status()))
                }
            }
            Err(_) => Err("Request failed".to_string())
        }
    }

    fn parse_graph_api_response(&self, response: &str, original_url: &str) -> Result<VideoInfo, String> {
        // Try to parse JSON response from Graph API
        if let Ok(json) = js_sys::JSON::parse(response) {
            let title = self.extract_json_field(&json, "title").unwrap_or_else(|| "Facebook Video".to_string());
            let description = self.extract_json_field(&json, "description").unwrap_or_default();
            let picture = self.extract_json_field(&json, "picture").unwrap_or_default();

            let video_id = self.extract_video_id(original_url)?;

            Ok(VideoInfo {
                title: if title.is_empty() { format!("Facebook Video {}", &video_id[..8]) } else { title },
                duration: "Unknown".to_string(),
                thumbnail: picture,
                qualities: self.generate_default_qualities(&video_id),
                video_id,
            })
        } else {
            Err("Invalid JSON response".to_string())
        }
    }

    fn parse_oembed_response(&self, response: &str, original_url: &str) -> Result<VideoInfo, String> {
        // Try to parse oEmbed JSON response
        if let Ok(json) = js_sys::JSON::parse(response) {
            let title = self.extract_json_field(&json, "title").unwrap_or_else(|| "Facebook Video".to_string());
            let thumbnail_url = self.extract_json_field(&json, "thumbnail_url").unwrap_or_default();

            let video_id = self.extract_video_id(original_url)?;

            Ok(VideoInfo {
                title: self.decode_html_entities(&title),
                duration: "Unknown".to_string(),
                thumbnail: thumbnail_url,
                qualities: self.generate_default_qualities(&video_id),
                video_id,
            })
        } else {
            Err("Invalid oEmbed JSON response".to_string())
        }
    }

    fn parse_embed_response(&self, response: &str, original_url: &str) -> Result<VideoInfo, String> {
        // Extract information from embed HTML
        let title = self.extract_title_from_html(response);
        let thumbnail = self.extract_thumbnail_from_html(response);
        let video_id = self.extract_video_id(original_url)?;

        Ok(VideoInfo {
            title: if title == "Facebook Video" { format!("Facebook Video {}", &video_id[..8]) } else { title },
            duration: "Unknown".to_string(),
            thumbnail,
            qualities: self.generate_default_qualities(&video_id),
            video_id,
        })
    }

    fn extract_json_field(&self, json: &JsValue, field: &str) -> Option<String> {
        if let Ok(value) = js_sys::Reflect::get(json, &JsValue::from_str(field)) {
            value.as_string()
        } else {
            None
        }
    }

    fn generate_default_qualities(&self, video_id: &str) -> Vec<VideoQuality> {
        vec![
            VideoQuality {
                quality: "720p HD".to_string(),
                size: "Unknown".to_string(),
                format: "MP4".to_string(),
                download_url: format!("https://video.xx.fbcdn.net/v/t42.9040-2/{}_hd.mp4", video_id),
                width: 1280,
                height: 720,
            },
            VideoQuality {
                quality: "480p SD".to_string(),
                size: "Unknown".to_string(),
                format: "MP4".to_string(),
                download_url: format!("https://video.xx.fbcdn.net/v/t42.9040-2/{}_sd.mp4", video_id),
                width: 854,
                height: 480,
            },
        ]
    }

    async fn simulate_browser_session_start(&self) {
        // Simulate realistic browser startup timing
        gloo::timers::future::TimeoutFuture::new(100).await;

        // Initialize session state (cookies, localStorage, etc.)
        self.initialize_browser_state().await;
    }

    async fn initialize_browser_state(&self) {
        let window = window().unwrap();

        // Set up realistic browser environment
        if let Some(storage) = window.local_storage().ok().flatten() {
            // Set browser fingerprint data
            let _ = storage.set_item("browser_session", "active");
            let _ = storage.set_item("user_preferences", "{}");
        }

        // Simulate browser history
        if let Some(history) = window.history().ok() {
            let _ = history.push_state_with_url(&JsValue::NULL, "", Some("about:blank"));
        }
    }

    async fn direct_browser_access(&self, url: &str) -> Result<String, String> {
        // Try multiple browser access methods

        // Method 1: Standard fetch with credentials
        if let Ok(html) = self.try_standard_fetch(url).await {
            return Ok(html);
        }

        // Method 2: No-CORS mode for content blocker bypass
        if let Ok(html) = self.try_no_cors_fetch(url).await {
            return Ok(html);
        }

        // Method 3: Same-origin mode with modified URL
        if let Ok(html) = self.try_same_origin_fetch(url).await {
            return Ok(html);
        }

        Err("All direct browser access methods failed".to_string())
    }

    async fn try_standard_fetch(&self, url: &str) -> Result<String, String> {
        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_credentials(web_sys::RequestCredentials::Include);
        opts.set_mode(web_sys::RequestMode::Cors);
        opts.set_cache(web_sys::RequestCache::NoCache);
        opts.set_redirect(web_sys::RequestRedirect::Follow);

        let request = web_sys::Request::new_with_str_and_init(url, &opts)
            .map_err(|_| "Failed to create standard request")?;

        // Set minimal headers to avoid content blocker detection
        self.set_minimal_browser_headers(&request)?;

        let window = window().unwrap();
        gloo::timers::future::TimeoutFuture::new(150).await;

        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|_| "Standard fetch failed")?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();

        if resp.ok() {
            let text = JsFuture::from(resp.text().unwrap()).await
                .map_err(|_| "Failed to get standard response text")?;
            Ok(text.as_string().unwrap_or_default())
        } else {
            Err("Standard fetch returned error status".to_string())
        }
    }

    async fn try_no_cors_fetch(&self, url: &str) -> Result<String, String> {
        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_mode(web_sys::RequestMode::NoCors);
        opts.set_credentials(web_sys::RequestCredentials::Omit);
        opts.set_cache(web_sys::RequestCache::NoCache);

        let request = web_sys::Request::new_with_str_and_init(url, &opts)
            .map_err(|_| "Failed to create no-cors request")?;

        let window = window().unwrap();
        gloo::timers::future::TimeoutFuture::new(100).await;

        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|_| "No-CORS fetch failed")?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();

        if resp.ok() {
            // Note: no-cors mode may not allow reading response text
            // This is mainly for triggering the request and potential caching
            Err("No-CORS mode - cannot read response".to_string())
        } else {
            Err("No-CORS fetch returned error status".to_string())
        }
    }

    async fn try_same_origin_fetch(&self, url: &str) -> Result<String, String> {
        // Try to access Facebook through a modified approach
        let modified_url = url.replace("www.facebook.com", "m.facebook.com");

        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_mode(web_sys::RequestMode::SameOrigin);
        opts.set_credentials(web_sys::RequestCredentials::SameOrigin);

        let request = web_sys::Request::new_with_str_and_init(&modified_url, &opts)
            .map_err(|_| "Failed to create same-origin request")?;

        let window = window().unwrap();
        gloo::timers::future::TimeoutFuture::new(100).await;

        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|_| "Same-origin fetch failed")?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();

        if resp.ok() {
            let text = JsFuture::from(resp.text().unwrap()).await
                .map_err(|_| "Failed to get same-origin response text")?;
            Ok(text.as_string().unwrap_or_default())
        } else {
            Err("Same-origin fetch returned error status".to_string())
        }
    }

    fn set_minimal_browser_headers(&self, request: &web_sys::Request) -> Result<(), String> {
        let headers = request.headers();

        // Use only essential headers to avoid content blocker detection
        let minimal_headers = [
            ("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"),
            ("Accept-Language", "en-US,en;q=0.9"),
            ("User-Agent", &self.user_agents[0]),
        ];

        for (name, value) in &minimal_headers {
            headers.set(name, value).map_err(|_| format!("Failed to set minimal header: {}", name))?;
        }

        Ok(())
    }

    fn set_authentic_browser_headers(&self, request: &web_sys::Request) -> Result<(), String> {
        let headers = request.headers();

        // Essential browser headers that Facebook expects
        let browser_headers = [
            ("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"),
            ("Accept-Language", "en-US,en;q=0.9"),
            ("Accept-Encoding", "gzip, deflate, br"),
            ("Cache-Control", "max-age=0"),
            ("Sec-Fetch-Dest", "document"),
            ("Sec-Fetch-Mode", "navigate"),
            ("Sec-Fetch-Site", "none"),
            ("Sec-Fetch-User", "?1"),
            ("Upgrade-Insecure-Requests", "1"),
            ("User-Agent", &self.user_agents[0]),
            ("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\""),
            ("sec-ch-ua-mobile", "?0"),
            ("sec-ch-ua-platform", "\"macOS\""),
        ];

        for (name, value) in &browser_headers {
            headers.set(name, value).map_err(|_| format!("Failed to set header: {}", name))?;
        }

        Ok(())
    }
    async fn iframe_browser_access(&self, url: &str) -> Result<String, String> {
        // Try multiple iframe approaches

        // Method 1: Standard iframe with sandbox
        if let Ok(html) = self.try_sandboxed_iframe(url).await {
            return Ok(html);
        }

        // Method 2: Iframe without sandbox (more permissive)
        if let Ok(html) = self.try_permissive_iframe(url).await {
            return Ok(html);
        }

        // Method 3: Dynamic script injection
        if let Ok(html) = self.try_script_injection(url).await {
            return Ok(html);
        }

        Err("All iframe access methods failed".to_string())
    }

    async fn try_sandboxed_iframe(&self, url: &str) -> Result<String, String> {
        let window = window().unwrap();
        let document = window.document().unwrap();

        let iframe = document.create_element("iframe")
            .map_err(|_| "Failed to create sandboxed iframe")?;

        iframe.set_attribute("src", url).map_err(|_| "Failed to set iframe src")?;
        iframe.set_attribute("sandbox", "allow-scripts allow-same-origin allow-forms allow-popups").map_err(|_| "Failed to set sandbox")?;
        iframe.set_attribute("style", "width: 1px; height: 1px; position: absolute; left: -9999px; opacity: 0; pointer-events: none;").map_err(|_| "Failed to set iframe style")?;

        document.body().unwrap().append_child(&iframe).map_err(|_| "Failed to append iframe")?;

        let load_result = self.wait_for_iframe_load(&iframe).await;
        let result = match load_result {
            Ok(()) => self.extract_iframe_content(&iframe).await,
            Err(e) => Err(e),
        };

        let _ = document.body().unwrap().remove_child(&iframe);
        result
    }

    async fn try_permissive_iframe(&self, url: &str) -> Result<String, String> {
        let window = window().unwrap();
        let document = window.document().unwrap();

        let iframe = document.create_element("iframe")
            .map_err(|_| "Failed to create permissive iframe")?;

        // Try mobile version for better compatibility
        let mobile_url = url.replace("www.facebook.com", "m.facebook.com");
        iframe.set_attribute("src", &mobile_url).map_err(|_| "Failed to set iframe src")?;
        iframe.set_attribute("style", "width: 1px; height: 1px; position: absolute; left: -9999px; opacity: 0; pointer-events: none;").map_err(|_| "Failed to set iframe style")?;

        document.body().unwrap().append_child(&iframe).map_err(|_| "Failed to append iframe")?;

        let load_result = self.wait_for_iframe_load(&iframe).await;
        let result = match load_result {
            Ok(()) => self.extract_iframe_content(&iframe).await,
            Err(e) => Err(e),
        };

        let _ = document.body().unwrap().remove_child(&iframe);
        result
    }

    async fn try_script_injection(&self, url: &str) -> Result<String, String> {
        let window = window().unwrap();
        let document = window.document().unwrap();

        // Create a script element that fetches the content
        let script = document.create_element("script")
            .map_err(|_| "Failed to create script element")?;

        // Use JSONP-like approach for cross-origin requests
        let script_content = format!(
            r#"
            (function() {{
                try {{
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', '{}', true);
                    xhr.withCredentials = true;
                    xhr.onreadystatechange = function() {{
                        if (xhr.readyState === 4 && xhr.status === 200) {{
                            window.facebookVideoContent = xhr.responseText;
                        }}
                    }};
                    xhr.send();
                }} catch(e) {{
                    console.log('Script injection failed:', e);
                }}
            }})();
            "#,
            url
        );

        script.set_text_content(Some(&script_content));
        document.head().unwrap().append_child(&script).map_err(|_| "Failed to append script")?;

        // Wait for potential response
        gloo::timers::future::TimeoutFuture::new(2000).await;

        // Check if content was retrieved
        if let Ok(content) = js_sys::Reflect::get(&window, &JsValue::from_str("facebookVideoContent")) {
            if let Some(content_str) = content.as_string() {
                if !content_str.is_empty() {
                    let _ = document.head().unwrap().remove_child(&script);
                    return Ok(content_str);
                }
            }
        }

        let _ = document.head().unwrap().remove_child(&script);
        Err("Script injection did not retrieve content".to_string())
    }

    async fn wait_for_iframe_load(&self, _iframe: &web_sys::Element) -> Result<(), String> {
        // Simplified iframe load waiting with timeout
        gloo::timers::future::TimeoutFuture::new(3000).await;
        Ok(())
    }

    async fn extract_iframe_content(&self, iframe: &web_sys::Element) -> Result<String, String> {
        // Try to access iframe content document
        if let Ok(iframe_element) = iframe.clone().dyn_into::<web_sys::HtmlIFrameElement>() {
            if let Some(content_document) = iframe_element.content_document() {
                if let Some(document_element) = content_document.document_element() {
                    return Ok(document_element.outer_html());
                }
            }
        }
        Err("Cannot access iframe content".to_string())
    }

    async fn service_worker_access(&self, url: &str) -> Result<String, String> {
        let window = window().unwrap();

        // Check if service workers are supported
        let navigator = window.navigator();
        if let Ok(service_worker) = js_sys::Reflect::get(&navigator, &JsValue::from_str("serviceWorker")) {
            if !service_worker.is_undefined() {
                // Try to register a service worker for CORS bypass
                return self.register_cors_service_worker(url).await;
            }
        }

        Err("Service workers not supported".to_string())
    }

    async fn register_cors_service_worker(&self, url: &str) -> Result<String, String> {
        // This is a placeholder for service worker implementation
        // In a real implementation, you would:
        // 1. Register a service worker that intercepts fetch requests
        // 2. Have the service worker modify headers and bypass CORS
        // 3. Return the fetched content through the service worker

        // For now, we'll use a direct fetch with modified approach
        self.fetch_with_service_worker_headers(url).await
    }

    async fn fetch_with_service_worker_headers(&self, url: &str) -> Result<String, String> {
        // Simulate service worker behavior with enhanced headers
        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_mode(web_sys::RequestMode::NoCors); // Try no-cors mode
        opts.set_credentials(web_sys::RequestCredentials::Omit);

        let request = web_sys::Request::new_with_str_and_init(url, &opts)
            .map_err(|_| "Failed to create service worker request")?;

        // Set minimal headers for no-cors mode
        let headers = request.headers();
        headers.set("User-Agent", &self.user_agents[0]).ok();

        let window = window().unwrap();
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|_| "Service worker request failed")?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();

        if resp.ok() {
            let text = JsFuture::from(resp.text().unwrap()).await
                .map_err(|_| "Failed to get service worker response text")?;
            Ok(text.as_string().unwrap_or_default())
        } else {
            Err("Service worker request returned error status".to_string())
        }
    }
    /// Enhanced proxy extraction with robust error handling
    async fn extract_via_proxy_services(&self, url: &str) -> Result<VideoInfo, String> {
        console::log_1(&format!("🌐 Trying {} proxy services...", self.proxy_services.len()).into());

        // Try each proxy service with enhanced error handling
        for (i, proxy) in self.proxy_services.iter().enumerate() {
            console::log_1(&format!("🔄 Trying proxy {}/{}: {}", i + 1, self.proxy_services.len(), proxy).into());

            // Try multiple approaches for each proxy
            match self.try_proxy_with_multiple_methods(proxy, url, i).await {
                Ok(info) => {
                    console::log_1(&format!("✅ Proxy {} succeeded!", proxy).into());
                    return Ok(info);
                },
                Err(proxy_error) => {
                    console::log_1(&format!("❌ Proxy {} failed: {}", proxy, proxy_error).into());
                }
            }

            // Add delay between proxy attempts to avoid rate limiting
            gloo::timers::future::TimeoutFuture::new(500).await;
        }

        console::log_1(&"❌ All proxy services failed".into());
        Err("All proxy services failed to extract video information".to_string())
    }

    async fn try_proxy_with_multiple_methods(&self, proxy: &str, url: &str, index: usize) -> Result<VideoInfo, String> {
        // Method 1: Standard proxy request
        if let Ok(info) = self.try_standard_proxy_request(proxy, url, index).await {
            return Ok(info);
        }

        // Method 2: Mobile version through proxy
        let mobile_url = url.replace("www.facebook.com", "m.facebook.com");
        if let Ok(info) = self.try_standard_proxy_request(proxy, &mobile_url, index).await {
            return Ok(info);
        }

        // Method 3: Different URL encoding
        if let Ok(info) = self.try_encoded_proxy_request(proxy, url, index).await {
            return Ok(info);
        }

        Err("All proxy methods failed for this service".to_string())
    }

    async fn try_standard_proxy_request(&self, proxy: &str, url: &str, index: usize) -> Result<VideoInfo, String> {
        let proxied_url = self.construct_proxy_url(proxy, url);

        match self.fetch_with_enhanced_headers(&proxied_url, index).await {
            Ok(html) => {
                if let Ok(info) = self.parse_facebook_html(&html, url) {
                    return Ok(info);
                }
                Err("Failed to parse HTML from proxy response".to_string())
            }
            Err(e) => Err(e)
        }
    }

    async fn try_encoded_proxy_request(&self, proxy: &str, url: &str, index: usize) -> Result<VideoInfo, String> {
        // Try with double encoding for problematic proxies
        let encoded_url = js_sys::encode_uri_component(url);
        let proxied_url = if proxy.contains("codetabs") {
            format!("{}{}", proxy, encoded_url)
        } else if proxy.contains("corsproxy.io") {
            format!("{}{}", proxy, encoded_url)
        } else {
            format!("{}{}", proxy, url)
        };

        match self.fetch_with_enhanced_headers(&proxied_url, index).await {
            Ok(html) => {
                if let Ok(info) = self.parse_facebook_html(&html, url) {
                    return Ok(info);
                }
                Err("Failed to parse HTML from encoded proxy response".to_string())
            }
            Err(e) => Err(e)
        }
    }

    fn construct_proxy_url(&self, proxy: &str, target_url: &str) -> String {
        if proxy.contains("codetabs.com") {
            format!("{}{}", proxy, js_sys::encode_uri_component(target_url))
        } else if proxy.contains("corsproxy.io") {
            format!("{}{}", proxy, js_sys::encode_uri_component(target_url))
        } else if proxy.contains("htmldriven.com") {
            format!("{}{}", proxy, js_sys::encode_uri_component(target_url))
        } else {
            format!("{}{}", proxy, target_url)
        }
    }

    async fn fetch_with_enhanced_headers(&self, url: &str, agent_index: usize) -> Result<String, String> {
        let user_agent = &self.user_agents[agent_index % self.user_agents.len()];

        // Create request with minimal headers to avoid proxy issues
        let request = Request::get(url)
            .header("User-Agent", user_agent)
            .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
            .header("Accept-Language", "en-US,en;q=0.9");

        match request.send().await {
            Ok(response) => {
                if response.ok() {
                    response.text().await.map_err(|_| "Failed to get response text".to_string())
                } else {
                    Err(format!("HTTP error: {}", response.status()))
                }
            }
            Err(_) => Err("Request failed".to_string())
        }
    }



    /// Advanced fetch with rotating user agents and headers
    async fn fetch_with_advanced_headers(&self, url: &str, agent_index: usize) -> Result<String, String> {
        let user_agent = &self.user_agents[agent_index % self.user_agents.len()];

        let request = Request::get(url)
            .header("User-Agent", user_agent)
            .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
            .header("Accept-Language", "en-US,en;q=0.5")
            .header("Accept-Encoding", "gzip, deflate, br")
            .header("DNT", "1")
            .header("Connection", "keep-alive")
            .header("Upgrade-Insecure-Requests", "1")
            .header("Sec-Fetch-Dest", "document")
            .header("Sec-Fetch-Mode", "navigate")
            .header("Sec-Fetch-Site", "cross-site")
            .header("Cache-Control", "max-age=0")
            .header("Referer", "https://www.google.com/");

        match request.send().await {
            Ok(response) => {
                if response.ok() {
                    response.text().await.map_err(|e| format!("Failed to get text: {:?}", e))
                } else {
                    Err(format!("HTTP error: {}", response.status()))
                }
            }
            Err(e) => Err(format!("Request failed: {:?}", e))
        }
    }







    fn parse_facebook_html(&self, html: &str, original_url: &str) -> Result<VideoInfo, String> {

        // Extract video ID
        let video_id = self.extract_video_id(original_url)?;

        // Extract title from various possible locations
        let raw_title = self.extract_title_from_html(html);
        let title = self.decode_html_entities(&raw_title);

        // Extract thumbnail
        let thumbnail = self.extract_thumbnail_from_html(html);

        // Extract duration
        let duration = self.extract_duration_from_html(html);

        // Extract video URLs - this is the key part
        let qualities = self.extract_video_urls_from_html(html);

        if qualities.is_empty() {
            return Err("No video URLs found in Facebook page content".to_string());
        }

        Ok(VideoInfo {
            title,
            duration,
            thumbnail,
            qualities,
            video_id,
        })
    }

    fn extract_video_id(&self, url: &str) -> Result<String, String> {
        let patterns = [
            // Query parameter patterns (most common)
            r"[?&]v=(\d+)",                           // ?v=123456789 or &v=123456789
            r"facebook\.com/watch\?.*v=(\d+)",        // facebook.com/watch?v=123456789
            r"www\.facebook\.com/watch\?.*v=(\d+)",   // www.facebook.com/watch?v=123456789
            r"m\.facebook\.com/watch\?.*v=(\d+)",     // m.facebook.com/watch?v=123456789
            r"fb\.com/watch\?.*v=(\d+)",              // fb.com/watch?v=123456789

            // Path-based patterns
            r"facebook\.com/.*/videos/(\d+)",         // facebook.com/user/videos/123456789
            r"www\.facebook\.com/.*/videos/(\d+)",    // www.facebook.com/user/videos/123456789
            r"m\.facebook\.com/.*/videos/(\d+)",      // m.facebook.com/user/videos/123456789
            r"fb\.com/.*/videos/(\d+)",               // fb.com/user/videos/123456789

            // Reel patterns
            r"facebook\.com/reel/(\d+)",              // facebook.com/reel/123456789
            r"www\.facebook\.com/reel/(\d+)",         // www.facebook.com/reel/123456789
            r"m\.facebook\.com/reel/(\d+)",           // m.facebook.com/reel/123456789
            r"fb\.com/reel/(\d+)",                    // fb.com/reel/123456789

            // Post patterns
            r"facebook\.com/.*/posts/(\d+)",          // facebook.com/user/posts/123456789
            r"www\.facebook\.com/.*/posts/(\d+)",     // www.facebook.com/user/posts/123456789
            r"m\.facebook\.com/.*/posts/(\d+)",       // m.facebook.com/user/posts/123456789
            r"fb\.com/.*/posts/(\d+)",                // fb.com/user/posts/123456789
        ];

        for pattern in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(url) {
                    if let Some(id) = captures.get(1) {
                        return Ok(id.as_str().to_string());
                    }
                }
            }
        }

        Err("Could not extract video ID from URL".to_string())
    }

    fn extract_title_from_html(&self, html: &str) -> String {
        // Try multiple patterns for title extraction
        let patterns = [
            r#"<meta property="og:title" content="([^"]+)""#,
            r#"<meta name="twitter:title" content="([^"]+)""#,
            r#"<title>([^<]+)</title>"#,
            r#""title":"([^"]+)""#,
            r#""name":"([^"]+)""#,
            r#""video_title":"([^"]+)""#,
            r#""post_title":"([^"]+)""#,
            r#"data-title="([^"]+)""#,
            r#"aria-label="([^"]*video[^"]*)"#,
        ];

        for pattern in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(html) {
                    if let Some(title) = captures.get(1) {
                        let title_str = title.as_str().trim();

                        // Skip very short titles or generic ones
                        if title_str.len() > 10 &&
                           !title_str.to_lowercase().contains("facebook") &&
                           !title_str.to_lowercase().contains("watch") &&
                           !title_str.to_lowercase().contains("video") {
                            return title_str.to_string();
                        }
                    }
                }
            }
        }

        // Try to extract from page content patterns
        let content_patterns = [
            r#"<h1[^>]*>([^<]+)</h1>"#,
            r#"<h2[^>]*>([^<]+)</h2>"#,
            r#"class="[^"]*title[^"]*"[^>]*>([^<]+)<"#,
        ];

        for pattern in &content_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(html) {
                    if let Some(title) = captures.get(1) {
                        let title_str = title.as_str().trim();
                        if title_str.len() > 10 {
                            return title_str.to_string();
                        }
                    }
                }
            }
        }

        "Facebook Video".to_string()
    }

    fn extract_thumbnail_from_html(&self, html: &str) -> String {
        let patterns = [
            r#"<meta property="og:image" content="([^"]+)""#,
            r#"<meta name="twitter:image" content="([^"]+)""#,
            r#""thumbnail":"([^"]+)""#,
        ];

        for pattern in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(html) {
                    if let Some(thumbnail) = captures.get(1) {
                        return thumbnail.as_str().replace("\\u0026", "&");
                    }
                }
            }
        }

        String::new()
    }

    fn extract_duration_from_html(&self, html: &str) -> String {
        let patterns = [
            r#"<meta property="video:duration" content="(\d+)""#,
            r#""duration":(\d+)"#,
            r#""length_in_second":(\d+)"#,
            r#""playable_duration_in_ms":(\d+)"#,
            r#""duration_ms":(\d+)"#,
            r#""video_duration":(\d+)"#,
            r#"duration["\s]*:["\s]*(\d+)"#,
            r#"lengthSeconds["\s]*:["\s]*["\s]*(\d+)["\s]*"#,
        ];

        for pattern in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(html) {
                    if let Some(duration) = captures.get(1) {
                        if let Ok(mut seconds) = duration.as_str().parse::<u32>() {
                            // Convert milliseconds to seconds if needed
                            if pattern.contains("_ms") && seconds > 10000 {
                                seconds = seconds / 1000;
                            }
                            return self.format_duration(seconds);
                        }
                    }
                }
            }
        }

        // Try to extract from time format (MM:SS or HH:MM:SS)
        let time_patterns = [
            r#"(\d{1,2}):(\d{2}):(\d{2})"#, // HH:MM:SS
            r#"(\d{1,2}):(\d{2})"#,        // MM:SS
        ];

        for pattern in &time_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(html) {
                    if captures.len() == 4 { // HH:MM:SS
                        if let (Ok(hours), Ok(minutes), Ok(seconds)) = (
                            captures.get(1).unwrap().as_str().parse::<u32>(),
                            captures.get(2).unwrap().as_str().parse::<u32>(),
                            captures.get(3).unwrap().as_str().parse::<u32>(),
                        ) {
                            let total_seconds = hours * 3600 + minutes * 60 + seconds;
                            return self.format_duration(total_seconds);
                        }
                    } else if captures.len() == 3 { // MM:SS
                        if let (Ok(minutes), Ok(seconds)) = (
                            captures.get(1).unwrap().as_str().parse::<u32>(),
                            captures.get(2).unwrap().as_str().parse::<u32>(),
                        ) {
                            let total_seconds = minutes * 60 + seconds;
                            return self.format_duration(total_seconds);
                        }
                    }
                }
            }
        }

        "Unknown".to_string()
    }

    fn extract_video_urls_from_html(&self, html: &str) -> Vec<VideoQuality> {
        let mut qualities = Vec::new();

        // Facebook video URL patterns - these change frequently
        let patterns = [
            (r#""hd_src":"([^"]+)""#, "720p HD", 1280, 720),
            (r#""sd_src":"([^"]+)""#, "480p SD", 854, 480),
            (r#""playable_url":"([^"]+)""#, "Auto", 0, 0),
            (r#""browser_native_hd_url":"([^"]+)""#, "720p HD", 1280, 720),
            (r#""browser_native_sd_url":"([^"]+)""#, "480p SD", 854, 480),
            (r#""playable_url_quality_hd":"([^"]+)""#, "720p HD", 1280, 720),
            (r#""dash_manifest":"([^"]+)""#, "DASH", 0, 0),
        ];

        for (pattern, quality_name, width, height) in &patterns {
            if let Ok(regex) = Regex::new(pattern) {
                for captures in regex.captures_iter(html) {
                    if let Some(url_match) = captures.get(1) {
                        let url = url_match.as_str()
                            .replace("\\u0026", "&")
                            .replace("\\/", "/")
                            .replace("\\\"", "\"");

                        if !url.is_empty() && url.starts_with("http") {
                            qualities.push(VideoQuality {
                                quality: quality_name.to_string(),
                                size: "Unknown".to_string(),
                                format: "MP4".to_string(),
                                download_url: url,
                                width: *width,
                                height: *height,
                            });
                        }
                    }
                }
            }
        }

        // Remove duplicates
        qualities.sort_by(|a, b| a.quality.cmp(&b.quality));
        qualities.dedup_by(|a, b| a.download_url == b.download_url);

        qualities
    }



    fn format_duration(&self, seconds: u32) -> String {
        let hours = seconds / 3600;
        let minutes = (seconds % 3600) / 60;
        let secs = seconds % 60;

        if hours > 0 {
            format!("{}:{:02}:{:02}", hours, minutes, secs)
        } else {
            format!("{}:{:02}", minutes, secs)
        }
    }

    fn decode_html_entities(&self, text: &str) -> String {
        let mut result = text.to_string();

        // Common HTML entities
        let entities = [
            ("&amp;", "&"),
            ("&lt;", "<"),
            ("&gt;", ">"),
            ("&quot;", "\""),
            ("&apos;", "'"),
            ("&#39;", "'"),
            ("&#x27;", "'"),
            ("&#x2F;", "/"),
            ("&#x5C;", "\\"),
            ("&#xb7;", "·"),
            ("&#x1f614;", "😔"),
            ("&#x1f62d;", "😭"),
            ("&#x1f60d;", "😍"),
            ("&#x1f602;", "😂"),
            ("&#x1f44d;", "👍"),
            ("&#x1f44e;", "👎"),
            ("&#x1f495;", "💕"),
            ("&#x1f496;", "💖"),
            ("&#x1f497;", "💗"),
            ("&#x1f498;", "💘"),
            ("&#x1f499;", "💙"),
            ("&#x1f49a;", "💚"),
            ("&#x1f49b;", "💛"),
            ("&#x1f49c;", "💜"),
            ("&#x1f49d;", "💝"),
            ("&#x1f49e;", "💞"),
            ("&#x1f49f;", "💟"),
        ];

        for (entity, replacement) in &entities {
            result = result.replace(entity, replacement);
        }

        // Handle Vietnamese characters
        let vietnamese_entities = [
            ("&#xe0;", "à"), ("&#xe1;", "á"), ("&#xe2;", "â"), ("&#xe3;", "ã"),
            ("&#xe8;", "è"), ("&#xe9;", "é"), ("&#xea;", "ê"), ("&#xeb;", "ë"),
            ("&#xec;", "ì"), ("&#xed;", "í"), ("&#xee;", "î"), ("&#xef;", "ï"),
            ("&#xf2;", "ò"), ("&#xf3;", "ó"), ("&#xf4;", "ô"), ("&#xf5;", "õ"),
            ("&#xf9;", "ù"), ("&#xfa;", "ú"), ("&#xfb;", "û"), ("&#xfc;", "ü"),
            ("&#x1ea1;", "ạ"), ("&#x1ea3;", "ả"), ("&#x1ea5;", "ấ"), ("&#x1ea7;", "ầ"),
            ("&#x1ea9;", "ẩ"), ("&#x1eab;", "ẫ"), ("&#x1ead;", "ậ"), ("&#x1eaf;", "ắ"),
            ("&#x1eb1;", "ằ"), ("&#x1eb3;", "ẳ"), ("&#x1eb5;", "ẵ"), ("&#x1eb7;", "ặ"),
            ("&#x1eb9;", "ẹ"), ("&#x1ebb;", "ẻ"), ("&#x1ebd;", "ẽ"), ("&#x1ebf;", "ế"),
            ("&#x1ec1;", "ề"), ("&#x1ec3;", "ể"), ("&#x1ec5;", "ễ"), ("&#x1ec7;", "ệ"),
            ("&#x1ec9;", "ỉ"), ("&#x1ecb;", "ị"), ("&#x1ecd;", "ọ"), ("&#x1ecf;", "ỏ"),
            ("&#x1ed1;", "ố"), ("&#x1ed3;", "ồ"), ("&#x1ed5;", "ổ"), ("&#x1ed7;", "ỗ"),
            ("&#x1ed9;", "ộ"), ("&#x1edb;", "ớ"), ("&#x1edd;", "ờ"), ("&#x1edf;", "ở"),
            ("&#x1ee1;", "ỡ"), ("&#x1ee3;", "ợ"), ("&#x1ee5;", "ụ"), ("&#x1ee7;", "ủ"),
            ("&#x1ee9;", "ứ"), ("&#x1eeb;", "ừ"), ("&#x1eed;", "ử"), ("&#x1eef;", "ữ"),
            ("&#x1ef1;", "ự"), ("&#x1ef3;", "ỳ"), ("&#x1ef5;", "ỵ"), ("&#x1ef7;", "ỷ"),
            ("&#x1ef9;", "ỹ"), ("&#x111;", "đ"), ("&#x110;", "Đ"),
            ("&#x1b0;", "ư"), ("&#x1af;", "Ư"), ("&#x1a1;", "ơ"), ("&#x1a0;", "Ơ"),
            ("&#x1eeb;", "ừ"), ("&#x1eed;", "ử"), ("&#x1eef;", "ữ"), ("&#x1ef1;", "ự"),
            ("&#x1edd;", "ờ"), ("&#x1edf;", "ở"), ("&#x1ee1;", "ỡ"), ("&#x1ee3;", "ợ"),
        ];

        for (entity, replacement) in &vietnamese_entities {
            result = result.replace(entity, replacement);
        }

        // Handle numeric entities (decimal)
        if let Ok(regex) = Regex::new(r"&#(\d+);") {
            for captures in regex.captures_iter(&result.clone()) {
                if let Some(num_str) = captures.get(1) {
                    if let Ok(num) = num_str.as_str().parse::<u32>() {
                        if let Some(ch) = char::from_u32(num) {
                            let entity = format!("&#{};", num);
                            result = result.replace(&entity, &ch.to_string());
                        }
                    }
                }
            }
        }

        // Handle hexadecimal entities
        if let Ok(regex) = Regex::new(r"&#x([0-9a-fA-F]+);") {
            for captures in regex.captures_iter(&result.clone()) {
                if let Some(hex_str) = captures.get(1) {
                    if let Ok(num) = u32::from_str_radix(hex_str.as_str(), 16) {
                        if let Some(ch) = char::from_u32(num) {
                            let entity = format!("&#x{};", hex_str.as_str());
                            result = result.replace(&entity, &ch.to_string());
                        }
                    }
                }
            }
        }

        // Clean up extra whitespace and trim
        result = result.trim().to_string();

        // Remove multiple spaces
        if let Ok(regex) = Regex::new(r"\s+") {
            result = regex.replace_all(&result, " ").to_string();
        }

        // Truncate very long titles
        if result.len() > 100 {
            result = format!("{}...", &result[..97]);
        }

        result
    }

    /// Download video using optimized browser capabilities with client-side proxy
    pub async fn download_video(&self, download_url: &str, filename: &str) -> Result<(), String> {
        console::log_1(&format!("📥 Starting download process...").into());
        console::log_1(&format!("🔗 Download URL: {}", download_url).into());
        console::log_1(&format!("📁 Filename: {}", filename).into());

        // Method 1: Try client-side proxy download (NEW!)
        console::log_1(&"🔄 Method 1: Trying client-side proxy download...".into());
        match self.try_client_side_proxy_download(download_url, filename).await {
            Ok(()) => {
                console::log_1(&"✅ Client-side proxy download succeeded!".into());
                return Ok(());
            },
            Err(e) => {
                console::log_1(&format!("❌ Client-side proxy download failed: {}", e).into());
            }
        }

        // Method 2: Try direct browser download
        console::log_1(&"🎯 Method 2: Trying direct browser download...".into());
        match self.try_direct_browser_download(download_url, filename).await {
            Ok(()) => {
                console::log_1(&"✅ Direct browser download succeeded!".into());
                return Ok(());
            },
            Err(e) => {
                console::log_1(&format!("❌ Direct browser download failed: {}", e).into());
            }
        }

        // Method 3: Fallback to creating download link
        console::log_1(&"🔗 Method 3: Trying download link creation...".into());
        match self.try_download_link_creation(download_url, filename) {
            Ok(()) => {
                console::log_1(&"✅ Download link creation succeeded!".into());
                Ok(())
            },
            Err(e) => {
                console::log_1(&format!("❌ Download link creation failed: {}", e).into());
                Err(format!("All download methods failed. Proxy: {}, Direct: {}, Link: {}", e, e, e))
            }
        }
    }

    /// Client-side proxy download using the same proxy services that worked for extraction
    async fn try_client_side_proxy_download(&self, download_url: &str, filename: &str) -> Result<(), String> {
        console::log_1(&"🔄 Setting up client-side proxy for download...".into());

        // Use the same proxy services that successfully extracted the video info (in optimized order)
        let working_proxies = vec![
            "https://proxy.cors.sh/",                          // This one works! Try it first
            "https://corsproxy.io/?",
            "https://cors.bridged.cc/",
            "https://api.codetabs.com/v1/proxy?quest=",
        ];

        for (i, proxy) in working_proxies.iter().enumerate() {
            console::log_1(&format!("🌐 Trying proxy {}/{}: {}", i + 1, working_proxies.len(), proxy).into());

            match self.download_via_proxy(proxy, download_url, filename).await {
                Ok(()) => {
                    console::log_1(&format!("✅ Proxy download succeeded with: {}", proxy).into());
                    return Ok(());
                },
                Err(e) => {
                    console::log_1(&format!("❌ Proxy {} failed: {}", proxy, e).into());
                    // Add small delay between proxy attempts
                    gloo::timers::future::TimeoutFuture::new(200).await;
                }
            }
        }

        Err("All proxy download methods failed".to_string())
    }

    async fn download_via_proxy(&self, proxy: &str, download_url: &str, filename: &str) -> Result<(), String> {
        console::log_1(&format!("📡 Downloading via proxy: {}", proxy).into());

        // Construct the proxied URL
        let proxied_url = if proxy.contains("codetabs.com") {
            format!("{}{}", proxy, js_sys::encode_uri_component(download_url))
        } else if proxy.contains("corsproxy.io") {
            format!("{}{}", proxy, js_sys::encode_uri_component(download_url))
        } else {
            format!("{}{}", proxy, download_url)
        };

        console::log_1(&format!("🔗 Proxied URL: {}", proxied_url).into());

        // Create request with appropriate headers
        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_mode(web_sys::RequestMode::Cors);
        opts.set_credentials(web_sys::RequestCredentials::Omit);

        let request = web_sys::Request::new_with_str_and_init(&proxied_url, &opts)
            .map_err(|e| format!("Failed to create proxy request: {:?}", e))?;

        // Set headers that worked for extraction
        request.headers().set("Accept", "*/*").ok();
        request.headers().set("User-Agent", &self.user_agents[0]).ok();

        console::log_1(&"📤 Sending proxy request...".into());
        let window = window().unwrap();
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|e| format!("Proxy request failed: {:?}", e))?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();
        console::log_1(&format!("📊 Proxy response status: {}", resp.status()).into());

        if resp.ok() {
            console::log_1(&"📥 Getting video data from proxy...".into());
            let array_buffer = JsFuture::from(resp.array_buffer().unwrap()).await
                .map_err(|e| format!("Failed to get proxy response data: {:?}", e))?;

            let uint8_array = js_sys::Uint8Array::new(&array_buffer);
            let data_size = uint8_array.length() as usize;
            console::log_1(&format!("📊 Downloaded {} bytes via proxy", data_size).into());

            if data_size == 0 {
                return Err("Proxy returned empty response".to_string());
            }

            let mut data = vec![0; data_size];
            uint8_array.copy_to(&mut data);

            // Trigger download using blob
            console::log_1(&"💾 Creating blob download...".into());
            self.trigger_browser_download(&data, filename, "video/mp4");
            console::log_1(&"✅ Proxy download completed successfully!".into());
            Ok(())
        } else {
            Err(format!("Proxy request failed with status: {}", resp.status()))
        }
    }

    async fn try_direct_browser_download(&self, download_url: &str, filename: &str) -> Result<(), String> {
        console::log_1(&"🌐 Creating fetch request with credentials...".into());

        // Use browser's fetch with minimal headers
        let opts = web_sys::RequestInit::new();
        opts.set_method("GET");
        opts.set_credentials(web_sys::RequestCredentials::Include);

        let request = web_sys::Request::new_with_str_and_init(download_url, &opts)
            .map_err(|e| format!("Failed to create download request: {:?}", e))?;

        // Add essential headers
        request.headers().set("Referer", "https://www.facebook.com/").ok();
        request.headers().set("User-Agent", &self.user_agents[0]).ok();
        console::log_1(&"📋 Headers set: Referer and User-Agent".into());

        console::log_1(&"📡 Sending fetch request...".into());
        let window = window().unwrap();
        let resp_value = JsFuture::from(window.fetch_with_request(&request)).await
            .map_err(|e| {
                let error_msg = format!("Download request failed: {:?}", e);
                console::log_1(&format!("❌ Fetch failed: {}", error_msg).into());
                error_msg
            })?;

        let resp: web_sys::Response = resp_value.dyn_into().unwrap();
        console::log_1(&format!("📊 Response status: {}", resp.status()).into());
        console::log_1(&format!("✅ Response OK: {}", resp.ok()).into());

        if resp.ok() {
            console::log_1(&"📥 Getting response data as array buffer...".into());
            let array_buffer = JsFuture::from(resp.array_buffer().unwrap()).await
                .map_err(|e| {
                    let error_msg = format!("Failed to get video data: {:?}", e);
                    console::log_1(&format!("❌ Array buffer failed: {}", error_msg).into());
                    error_msg
                })?;

            let uint8_array = js_sys::Uint8Array::new(&array_buffer);
            let data_size = uint8_array.length() as usize;
            console::log_1(&format!("📊 Downloaded {} bytes", data_size).into());

            let mut data = vec![0; data_size];
            uint8_array.copy_to(&mut data);

            // Trigger download in browser
            console::log_1(&"💾 Triggering browser download...".into());
            self.trigger_browser_download(&data, filename, "video/mp4");
            console::log_1(&"✅ Browser download triggered successfully!".into());
            Ok(())
        } else {
            let error_msg = format!("Download failed with status: {}", resp.status());
            console::log_1(&format!("❌ {}", error_msg).into());
            Err(error_msg)
        }
    }

    fn try_download_link_creation(&self, download_url: &str, filename: &str) -> Result<(), String> {
        console::log_1(&"🔗 Creating download link element...".into());

        let window = window().unwrap();
        let document = window.document().unwrap();

        // Create download link
        let link = document.create_element("a")
            .map_err(|e| format!("Failed to create link element: {:?}", e))?;

        link.set_attribute("href", download_url)
            .map_err(|e| format!("Failed to set href: {:?}", e))?;
        link.set_attribute("download", filename)
            .map_err(|e| format!("Failed to set download attribute: {:?}", e))?;
        link.set_attribute("target", "_blank")
            .map_err(|e| format!("Failed to set target: {:?}", e))?;
        link.set_attribute("style", "display: none;")
            .map_err(|e| format!("Failed to set style: {:?}", e))?;

        console::log_1(&"📎 Link attributes set successfully".into());

        // Add to document and auto-click
        document.body().unwrap().append_child(&link)
            .map_err(|e| format!("Failed to append link: {:?}", e))?;

        console::log_1(&"🖱️ Triggering link click...".into());
        if let Ok(html_link) = link.clone().dyn_into::<web_sys::HtmlElement>() {
            html_link.click();
            console::log_1(&"✅ Link clicked successfully!".into());
        } else {
            console::log_1(&"❌ Failed to cast link to HtmlElement".into());
            return Err("Failed to cast link to HtmlElement".to_string());
        }

        // Cleanup
        document.body().unwrap().remove_child(&link)
            .map_err(|e| format!("Failed to remove link: {:?}", e))?;

        console::log_1(&"🧹 Link cleanup completed".into());
        Ok(())
    }

    fn trigger_browser_download(&self, data: &[u8], filename: &str, mime_type: &str) {
        use web_sys::{Blob, BlobPropertyBag, Url};

        let window = window().unwrap();
        let document = window.document().unwrap();

        // Create blob
        let array = js_sys::Uint8Array::new_with_length(data.len() as u32);
        array.copy_from(data);

        let blob_parts = Array::new();
        blob_parts.push(&array);

        let blob_property_bag = BlobPropertyBag::new();
        blob_property_bag.set_type(mime_type);

        let blob = Blob::new_with_u8_array_sequence_and_options(&blob_parts, &blob_property_bag).unwrap();

        // Create download link
        let url = Url::create_object_url_with_blob(&blob).unwrap();
        let link = document.create_element("a").unwrap();
        link.set_attribute("href", &url).unwrap();
        link.set_attribute("download", filename).unwrap();
        link.set_attribute("style", "display: none").unwrap();

        document.body().unwrap().append_child(&link).unwrap();

        // Trigger download
        let html_link = link.clone().dyn_into::<web_sys::HtmlElement>().unwrap();
        html_link.click();

        // Cleanup
        document.body().unwrap().remove_child(&link).unwrap();
        Url::revoke_object_url(&url).unwrap();
    }
}
