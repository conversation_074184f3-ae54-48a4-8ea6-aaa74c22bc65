@theme {
  /* Colors */
  --color-primary: #18a058;
  --color-primary-hover: #36ad6a;
  --color-primary-pressed: #0c7a43;
  --color-primary-faded: #18a0582f;

  --color-success: #18a058;
  --color-success-hover: #36ad6a;
  --color-success-pressed: #0c7a43;
  --color-success-faded: #18a0582f;

  --color-warning: #f59e0b;
  --color-warning-hover: #f59e0b;
  --color-warning-pressed: #f59e0b;
  --color-warning-faded: #f59e0b2f;

  --color-error: #d03050;
  --color-error-hover: #de576d;
  --color-error-pressed: #ab1f3f;
  --color-error-faded: #d030502a;

  --color-surface-light: #ffffff;
  --color-surface-dark: #232323;

  --color-background-light: #f1f5f9;
  --color-background-dark: #1c1c1c;

  --color-text-base-light: #333639;
  --color-text-base-dark: #ffffffd1;

  --color-text-muted-light: #767c82;
  --color-text-muted-dark: #ffffff80;

  --color-border-light: #efeff5;
  --color-border-dark: #282828;

  --color-input-bg-light: #ffffff;
  --color-input-bg-dark: #333333;

  --color-input-border-light: #e0e0e69e;
  --color-input-border-dark: #333333;

  --color-input-focus-light: #ffffff;
  --color-input-focus-dark: #1ea54c1a;

  --color-neutral-400: #a3a3a3;

  /* Spacing */
  --spacing-5px: 5px;
  --spacing-12px: 12px;
  --spacing-25px: 25px;
  --spacing-50px: 50px;
  --spacing-90%: 90%;

  /* Font Weight */
  --font-weight-500: 500;

  /* Box Shadow */
  --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-card-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-card-dark: 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-card-dark-hover: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-dropdown: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  --shadow-dropdown-dark: rgba(0, 0, 0, 0.2) 0px 8px 24px;

  /* Border Radius */
  --rounded: 4px;
}

/* Content paths for Tailwind CSS */
@content "./src/**/*.rs";
@content "./index.html";

/* Dark mode configuration */
@dark-mode {
  class: "dark";
}
