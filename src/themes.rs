use crate::stores::use_style_store;
use serde::{Deserialize, Serialize};
use std::rc::Rc;
use yew::prelude::*;

/// Represents text colors in a theme
///
/// Contains base and muted text colors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextColors {
    /// Base text color
    pub base_color: String,
    /// Muted (secondary) text color
    pub muted_color: String,
}

/// Represents a set of colors for different states of a UI element
///
/// Contains colors for normal, hover, pressed, and faded states
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorSet {
    /// Normal state color
    pub color: String,
    /// Hover state color
    pub color_hover: String,
    /// Pressed state color
    pub color_pressed: String,
    /// Faded state color
    pub color_faded: String,
}

/// Represents a set of colors for different states of default UI elements
///
/// Contains colors for normal, hover, and pressed states
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DefaultColorSet {
    /// Normal state color
    pub color: String,
    /// Hover state color
    pub color_hover: String,
    /// Pressed state color
    pub color_pressed: String,
}

/// Represents the theme for card UI elements
///
/// Contains background and border colors for cards
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CardTheme {
    /// Background color for cards
    pub background_color: String,
    /// Border color for cards
    pub border_color: String,
}

/// Represents the theme for input UI elements
///
/// Contains background, border, and focus state colors for inputs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InputTheme {
    /// Background color for inputs
    pub background_color: String,
    /// Border color for inputs
    pub border_color: String,
    /// Focus state theme for inputs
    pub focus: InputFocusTheme,
}

/// Represents the theme for input UI elements in focus state
///
/// Contains background color for inputs when focused
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InputFocusTheme {
    /// Background color for inputs when focused
    pub background_color: String,
}

/// Represents a complete theme with color definitions for UI elements
///
/// This struct contains all the color values used throughout the application
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Theme {
    /// Background color for the application
    pub background: String,
    /// Text colors for the application
    pub text: TextColors,
    /// Default color set for UI elements
    pub default: DefaultColorSet,
    /// Primary color set for UI elements
    pub primary: ColorSet,
    /// Warning color set for UI elements
    pub warning: ColorSet,
    /// Success color set for UI elements
    pub success: ColorSet,
    /// Error color set for UI elements
    pub error: ColorSet,
    /// Card theme for card UI elements
    pub card: CardTheme,
    /// Input theme for input UI elements
    pub input: InputTheme,
}

/// Container for both light and dark themes
///
/// This struct holds both the light and dark themes for the application
#[derive(Clone)]
pub struct AppThemes {
    /// Light theme
    pub light: Theme,
    /// Dark theme
    pub dark: Theme,
}

impl AppThemes {}

/// Hook to use the current theme based on the application's style state
///
/// This hook returns the current theme (light or dark) based on the application's style state
#[hook]
pub fn use_theme() -> Rc<Theme> {
    let style_store = use_style_store();
    let is_dark_theme = style_store.is_dark_theme;
    let themes = get_app_themes();

    Rc::new(if is_dark_theme {
        themes.dark.clone()
    } else {
        themes.light.clone()
    })
}

/// Returns the application's themes (light and dark)
///
/// This function creates and returns both the light and dark themes for the application
pub fn get_app_themes() -> AppThemes {
    AppThemes {
        light: Theme {
            background: "#ffffff".to_string(),
            text: TextColors {
                base_color: "#333639".to_string(),
                muted_color: "#767c82".to_string(),
            },
            default: DefaultColorSet {
                color: "rgba(46, 51, 56, 0.05)".to_string(),
                color_hover: "rgba(46, 51, 56, 0.09)".to_string(),
                color_pressed: "rgba(46, 51, 56, 0.22)".to_string(),
            },
            primary: ColorSet {
                color: "#18a058".to_string(),
                color_hover: "#1ea54c".to_string(),
                color_pressed: "#0C7A43".to_string(),
                color_faded: "#18a0582f".to_string(),
            },
            warning: ColorSet {
                color: "#f59e0b".to_string(),
                color_hover: "#f59e0b".to_string(),
                color_pressed: "#f59e0b".to_string(),
                color_faded: "#f59e0b2f".to_string(),
            },
            success: ColorSet {
                color: "#18a058".to_string(),
                color_hover: "#36ad6a".to_string(),
                color_pressed: "#0c7a43".to_string(),
                color_faded: "#18a0582f".to_string(),
            },
            error: ColorSet {
                color: "#d03050".to_string(),
                color_hover: "#de576d".to_string(),
                color_pressed: "#ab1f3f".to_string(),
                color_faded: "#d030502a".to_string(),
            },
            card: CardTheme {
                background_color: "#ffffff".to_string(),
                border_color: "#efeff5".to_string(),
            },
            input: InputTheme {
                background_color: "#ffffff".to_string(),
                border_color: "#e0e0e69e".to_string(),
                focus: InputFocusTheme {
                    background_color: "#ffffff".to_string(),
                },
            },
        },
        dark: Theme {
            background: "#1e1e1e".to_string(),
            text: TextColors {
                base_color: "#ffffffd1".to_string(),
                muted_color: "#ffffff80".to_string(),
            },
            default: DefaultColorSet {
                color: "rgba(255, 255, 255, 0.08)".to_string(),
                color_hover: "rgba(255, 255, 255, 0.12)".to_string(),
                color_pressed: "rgba(255, 255, 255, 0.24)".to_string(),
            },
            primary: ColorSet {
                color: "#1ea54c".to_string(),
                color_hover: "#36AD6A".to_string(),
                color_pressed: "#0C7A43".to_string(),
                color_faded: "#18a0582f".to_string(),
            },
            warning: ColorSet {
                color: "#f59e0b".to_string(),
                color_hover: "#f59e0b".to_string(),
                color_pressed: "#f59e0b".to_string(),
                color_faded: "#f59e0b2f".to_string(),
            },
            success: ColorSet {
                color: "#18a058".to_string(),
                color_hover: "#36ad6a".to_string(),
                color_pressed: "#0c7a43".to_string(),
                color_faded: "#18a0582f".to_string(),
            },
            error: ColorSet {
                color: "#e88080".to_string(),
                color_hover: "#e98b8b".to_string(),
                color_pressed: "#e57272".to_string(),
                color_faded: "#e8808029".to_string(),
            },
            card: CardTheme {
                background_color: "#232323".to_string(),
                border_color: "#282828".to_string(),
            },
            input: InputTheme {
                background_color: "#333333".to_string(),
                border_color: "#333333".to_string(),
                focus: InputFocusTheme {
                    background_color: "#1ea54c1a".to_string(),
                },
            },
        },
    }
}
