use gloo::storage::{LocalStorage, Storage};
use serde::{Deserialize, Serialize};
use std::ops::Deref;
use wasm_bindgen::{JsCast, prelude::*};
use web_sys::{MediaQueryList, window};
use yew::prelude::*;

const THEME_KEY: &str = "isDarkTheme";
const MENU_COLLAPSED_KEY: &str = "isMenuCollapsed";

/// Represents the application's style state
///
/// This struct holds information about the current theme, menu state, and screen size
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StyleState {
    /// Whether the dark theme is currently active
    pub is_dark_theme: bool,
    /// Whether the menu is currently collapsed
    pub is_menu_collapsed: bool,
    /// Whether the screen is considered small (mobile view)
    pub is_small_screen: bool,
}

impl Default for StyleState {
    fn default() -> Self {
        // Check if dark mode is preferred by the system
        let is_dark_preferred = if let Some(window) = window() {
            if let Ok(media_query) = window.match_media("(prefers-color-scheme: dark)") {
                media_query.is_some_and(|mql| mql.matches())
            } else {
                false
            }
        } else {
            false
        };

        // Check if small screen
        let is_small_screen = if let Some(window) = window() {
            if let Ok(media_query) = window.match_media("(max-width: 700px)") {
                media_query.is_some_and(|mql| mql.matches())
            } else {
                false
            }
        } else {
            false
        };

        // Try to get saved theme preference from local storage
        let is_dark_theme = LocalStorage::get(THEME_KEY).unwrap_or(is_dark_preferred);

        // Try to get menu collapsed state from local storage, default to small screen value
        let is_menu_collapsed = LocalStorage::get(MENU_COLLAPSED_KEY).unwrap_or(is_small_screen);

        // Note: Theme class is now applied in index.html script to prevent flash
        // ThemeProvider component will handle theme changes after initial load

        Self {
            is_dark_theme,
            is_menu_collapsed,
            is_small_screen,
        }
    }
}

/// Store for managing application style state
///
/// This store provides methods for accessing and modifying the application's style state
#[derive(Debug, Clone)]
pub struct StyleStore {
    /// The current style state
    state: UseStateHandle<StyleState>,
}

/// Hook to access the StyleStore from components
///
/// This hook provides access to the application's style state and methods to modify it
#[hook]
pub fn use_style_store() -> StyleStore {
    let state = use_state(StyleState::default);
    let state_clone = state.clone();

    // Set up media query listener for small screen
    use_effect_with((), move |_| {
        if let Some(window) = window() {
            if let Ok(Some(media_query)) = window.match_media("(max-width: 700px)") {
                let state_handle = state_clone.clone();
                let callback = Closure::wrap(Box::new(move |e: web_sys::Event| {
                    if let Some(mql) = e.target().and_then(|t| t.dyn_into::<MediaQueryList>().ok())
                    {
                        let mut new_state = (*state_handle).clone();
                        new_state.is_small_screen = mql.matches();
                        new_state.is_menu_collapsed = mql.matches();
                        state_handle.set(new_state);

                        // Update local storage
                        let _ = LocalStorage::set(MENU_COLLAPSED_KEY, mql.matches());
                    }
                }) as Box<dyn FnMut(_)>);

                let _ = media_query
                    .add_event_listener_with_callback("change", callback.as_ref().unchecked_ref());
                callback.forget(); // Prevent callback from being dropped
            }
        }

        || {}
    });

    StyleStore { state }
}

impl StyleStore {
    /// Toggles between light and dark theme
    ///
    /// This method toggles the theme, updates local storage, and applies the appropriate CSS classes
    pub fn toggle_dark_theme(&self) {
        let mut new_state = (*self.state).clone();
        new_state.is_dark_theme = !new_state.is_dark_theme;
        self.state.set(new_state.clone());

        // Update local storage
        let _ = LocalStorage::set(THEME_KEY, new_state.is_dark_theme);

        // Apply the dark class to the HTML element directly
        if let Some(window) = window() {
            if let Some(document) = window.document() {
                if let Some(html_element) = document.document_element() {
                    if new_state.is_dark_theme {
                        let _ = html_element.class_list().add_1("dark");
                        let _ = html_element.class_list().remove_1("light");
                    } else {
                        let _ = html_element.class_list().remove_1("dark");
                        let _ = html_element.class_list().add_1("light");
                    }
                }
            }
        }
    }

    /// Toggles the collapsed state of the menu
    ///
    /// This method toggles the menu collapsed state and updates local storage
    pub fn toggle_menu_collapsed(&self) {
        let mut new_state = (*self.state).clone();
        new_state.is_menu_collapsed = !new_state.is_menu_collapsed;
        self.state.set(new_state.clone());

        // Update local storage
        let _ = LocalStorage::set(MENU_COLLAPSED_KEY, new_state.is_menu_collapsed);
    }
}

/// Implements Deref to allow direct access to StyleState fields
///
/// This allows components to access StyleState fields directly from StyleStore
impl Deref for StyleStore {
    type Target = StyleState;

    fn deref(&self) -> &Self::Target {
        &self.state
    }
}
