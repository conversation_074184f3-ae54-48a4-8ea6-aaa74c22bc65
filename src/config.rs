use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct AppConfig {
    pub version: String,
    pub base_url: String,
    pub env: Environment,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum Environment {
    Production,
    Development,
    Preview,
    Test,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct PlausibleConfig {
    pub is_tracker_enabled: bool,
    pub domain: String,
    pub api_host: String,
    pub track_localhost: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub app: AppConfig,
    pub plausible: PlausibleConfig,
    pub show_banner: bool,
    pub show_sponsor_banner: bool,
}

impl Default for Config {
    fn default() -> Self {
        Config {
            app: AppConfig {
                version: env!("CARGO_PKG_VERSION").to_string(),
                base_url: "/".to_string(),
                env: Environment::Development,
            },
            plausible: PlausibleConfig {
                is_tracker_enabled: false,
                domain: "".to_string(),
                api_host: "".to_string(),
                track_localhost: false,
            },
            show_banner: false,
            show_sponsor_banner: false,
        }
    }
}
