use crate::components::{
    <PERSON>lapsibleToolMenu, CommandPalette, LocaleSelector, MenuLayout, NavbarButtons,
};
use crate::router::Route;
use crate::stores::use_style_store;
use crate::themes::use_theme;
use crate::tools::get_tools_by_category;
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Properties, PartialEq)]
pub struct BaseLayoutProps {
    #[prop_or_default]
    pub children: Children,
}

#[function_component(BaseLayout)]
pub fn base_layout(props: &BaseLayoutProps) -> Html {
    let style_store = use_style_store();
    let is_small_screen = style_store.is_small_screen;
    let _is_menu_collapsed = style_store.is_menu_collapsed;
    let _theme = use_theme();

    let toggle_menu = Callback::from(move |_| {
        style_store.toggle_menu_collapsed();
    });

    // Sider content
    let sider = html! {
        <>
            <Link<Route> to={Route::Home} classes="hero-wrapper">
                <div class="hero-gradient">
                    <img src="assets/hero-gradient.svg" alt="Hero Gradient" class="hero-svg" />
                </div>
                <div class="text-wrapper">
                    <div class="title">{"IT - TOOLS"}</div>
                    <div class="divider"></div>
                    <div class="subtitle">{"Handy tools for developers"}</div>
                </div>
            </Link<Route>>

            <div class="sider-content scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent dark:scrollbar-thumb-gray-700">
                if is_small_screen {
                    <div class="flex flex-col items-center">
                        <div class="w-[90%]">
                            <LocaleSelector />
                        </div>

                        <div class="flex justify-center mt-2">
                            <NavbarButtons />
                        </div>
                    </div>
                }

                // Tool categories
                <div>
                    <CollapsibleToolMenu tools_by_category={get_tools_by_category()} />
                </div>

                <div class="footer text-center text-[#838587] text-sm mt-5 py-5">
                    <div>
                        {"IT-Tools"}
                        <a href="https://github.com/CorentinTh/it-tools" target="_blank" rel="noopener" class="text-primary hover:text-primary-hover ml-1">
                            {"v1.0.0"}
                        </a>
                        {" - "}
                        <a href="https://github.com/CorentinTh/it-tools/tree/main" target="_blank" rel="noopener" class="text-primary hover:text-primary-hover">
                            {"main"}
                        </a>
                    </div>
                    <div>
                        {"© "}{chrono::Utc::now().format("%Y")}
                        <a href="https://corentin.tech" target="_blank" rel="noopener" class="text-primary hover:text-primary-hover ml-1">
                            {"Corentin Thomasset"}
                        </a>
                    </div>
                </div>
            </div>
        </>
    };

    // Header content
    let header = html! {
        <div class="navbar-container flex items-center justify-center py-0 px-[2%] gap-[1%] h-[50px]">
            <button
                class="bg-transparent border-none cursor-pointer flex items-center justify-center p-[1%] rounded-full text-[#4b5563] transition-all duration-200 hover:bg-[#f3f4f6] dark:text-[#d1d5db] dark:hover:bg-[#1f2937]"
                aria-label="Toggle Menu"
                onclick={toggle_menu}
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
            </button>

            <Link<Route> to={Route::Home} classes="bg-transparent border-none cursor-pointer flex items-center justify-center p-[1%] rounded-full text-[#4b5563] transition-all duration-200 hover:bg-[#f3f4f6] dark:text-[#d1d5db] dark:hover:bg-[#1f2937]">
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
            </Link<Route>>

            <div class="navbar-search-container flex-1 min-w-0 max-w-full px-[2%] h-[34px]">
                <CommandPalette />
            </div>

            if !is_small_screen {
                <div class="mx-[1%] flex-shrink-0 w-[100px] h-[34px]">
                    <LocaleSelector />
                </div>
                <div class="flex-shrink-0">
                    <NavbarButtons />
                </div>
            }

            <a
                href="https://www.buymeacoffee.com/cthmsst"
                target="_blank"
                rel="noopener"
                class="support-button px-[8px] py-[4px] text-xs flex items-center gap-[6px] ml-[1%] rounded-md border border-gray-200 dark:border-gray-600"
            >
                {"Buy me a coffee"}
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="flex-shrink-0">
                    <path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572"></path>
                </svg>
            </a>
        </div>
    };

    // Use the MenuLayout component
    html! {
        <MenuLayout
            class={classes!(
                is_small_screen.then_some("is-small-screen")
            )}
            sider={sider}
            content={header}
        >
            { for props.children.iter() }
        </MenuLayout>
    }
}
