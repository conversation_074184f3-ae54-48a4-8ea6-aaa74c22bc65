use crate::components::FavoriteButton;
use crate::tools::Tool;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ToolLayoutProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub tool_name: String,
    #[prop_or_default]
    pub tool_description: String,
}

#[function_component(ToolLayout)]
pub fn tool_layout(props: &ToolLayoutProps) -> Html {
    // Create a Tool struct for the FavoriteButton
    let tool = Tool {
        name: props.tool_name.clone(),
        description: props.tool_description.clone(),
        path: "".to_string(),
        category: "".to_string(),
        is_new: false,
        is_favorite: false,
        keywords: vec![],
    };

    html! {
        <div class="tool-layout">
            <div class="tool-header">
                <h1 class="tool-title">{&props.tool_name}</h1>
                <p class="tool-description">{&props.tool_description}</p>
                <FavoriteButton tool={tool} />
            </div>
            <div class="tool-content">
                { for props.children.iter() }
            </div>
        </div>
    }
}
