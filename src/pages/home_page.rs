use crate::components::{ColoredCard, SvgIcon, ToolCard};
use crate::stores::use_style_store;
use crate::tools::{get_tools, Tool};
use gloo::storage::{LocalStorage, Storage};
use yew::prelude::*;

const FAVORITE_TOOLS_KEY: &str = "favoriteToolsName";
const CONFIG_SHOW_BANNER: bool = true;

#[function_component(HomePage)]
pub fn home_page() -> Html {
    let _style_store = use_style_store();

    // Get tools from the tools module
    let tools = get_tools();

    // Get favorite tools
    let favorite_tool_names: Vec<String> =
        LocalStorage::get(FAVORITE_TOOLS_KEY).unwrap_or_else(|_| Vec::new());
    let favorite_tools: Vec<Tool> = tools
        .iter()
        .filter(|tool| favorite_tool_names.contains(&tool.name))
        .cloned()
        .collect();

    // Get new tools
    let new_tools: Vec<Tool> = tools.iter().filter(|tool| tool.is_new).cloned().collect();

    html! {
        <div class="pt-50px">
            <div class="grid-wrapper">
                if CONFIG_SHOW_BANNER {
                    <div class="grid grid-cols-1 gap-12px lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2 xl:grid-cols-4">
                        <ColoredCard title="Follow IT Tools" icon="heart">
                            {"Star us on "}
                            <a href="https://github.com/CorentinTh/it-tools" target="_blank" rel="noopener">
                                {"GitHub"}
                            </a>
                            {" and follow us on "}
                            <a href="https://x.com/ittoolsdottech" target="_blank" rel="noopener">
                                {"X"}
                            </a>
                            {". Thank you! "}
                            <SvgIcon name="heart" size="16" />
                        </ColoredCard>
                    </div>
                }

                if !favorite_tools.is_empty() {
                    <div class="transition-height">
                        <h3 class="mb-5px mt-25px text-neutral-400 font-500">
                            {"Favorite Tools"}
                        </h3>
                        <div class="grid grid-cols-1 gap-12px lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2 xl:grid-cols-4">
                            {
                                favorite_tools.iter().map(|tool| {
                                    let tool_clone = tool.clone();
                                    html! {
                                        <ToolCard tool={tool_clone} />
                                    }
                                }).collect::<Html>()
                            }
                        </div>
                    </div>
                }

                if !new_tools.is_empty() {
                    <div>
                        <h3 class="mb-5px mt-25px text-neutral-400 font-500">
                            {"Newest Tools"}
                        </h3>
                        <div class="grid grid-cols-1 gap-12px lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2 xl:grid-cols-4">
                            {
                                new_tools.iter().map(|tool| {
                                    let tool_clone = tool.clone();
                                    html! {
                                        <ToolCard tool={tool_clone} />
                                    }
                                }).collect::<Html>()
                            }
                        </div>
                    </div>
                }

                <h3 class="mb-5px mt-25px text-neutral-400 font-500">
                    {"All Tools"}
                </h3>
                <div class="grid grid-cols-1 gap-12px lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2 xl:grid-cols-4">
                    {
                        tools.iter().map(|tool| {
                            let tool_clone = tool.clone();
                            html! {
                                <ToolCard tool={tool_clone} />
                            }
                        }).collect::<Html>()
                    }
                </div>
            </div>
        </div>
    }
}
