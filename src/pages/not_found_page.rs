use crate::components::SvgIcon;
use crate::router::Route;
use crate::ui::Button;
use yew::prelude::*;

// Constants for better maintainability
const ICON_SIZE: &str = "90";
const ICON_NAME: &str = "kettle_steam_outline";

// CSS class constants to avoid duplication and typos
const CONTAINER_CLASSES: &str = "mt-20 flex flex-col items-center space-y-4 px-4";
const ICON_CLASSES: &str = "opacity-50 text-gray-400 dark:text-gray-500";
const HEADING_CLASSES: &str = "text-3xl font-bold text-gray-900 dark:text-white text-center";
const MESSAGE_CLASSES: &str = "text-gray-600 dark:text-gray-300 text-center max-w-md opacity-75";

#[function_component(NotFoundPage)]
pub fn not_found_page() -> Html {
    html! {
        <div class={CONTAINER_CLASSES}>
            // Large kettle icon
            <div class="mb-2" aria-hidden="true">
                <SvgIcon
                    name={ICON_NAME}
                    size={ICON_SIZE}
                    class={ICON_CLASSES}
                />
            </div>

            // Main heading
            <h1 class={HEADING_CLASSES}>
                {"404 Not Found"}
            </h1>

            // Error messages
            <p class={MESSAGE_CLASSES}>
                {"Sorry, this page does not seem to exist"}
            </p>

            <p class={MESSAGE_CLASSES}>
                {"Maybe the cache is doing tricky things, try force-refreshing?"}
            </p>

            // Navigation button
            <div class="mt-6">
                <Button
                    to={Some(Route::Home)}
                    r#type="primary"
                    aria_label={Some("Return to homepage".to_string())}
                >
                    {"Back home"}
                </Button>
            </div>
        </div>
    }
}
