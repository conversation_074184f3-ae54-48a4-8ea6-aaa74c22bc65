use crate::stores::use_style_store;
use gloo::storage::{LocalStorage, Storage};
use web_sys::{Document, window};
use yew::prelude::*;

const DARK_THEME_LOADED_KEY: &str = "darkThemeLoaded";
const LIGHT_THEME_LOADED_KEY: &str = "lightThemeLoaded";

/// ThemeProvider component that applies the 'dark' class to the HTML element
/// based on the theme state and lazily loads theme-specific CSS.
///
/// This component doesn't render anything visible, it just manages the theme class
/// on the HTML element and loads theme-specific CSS on demand.
#[function_component(ThemeProvider)]
pub fn theme_provider() -> Html {
    let style_store = use_style_store();
    let is_dark_theme = style_store.is_dark_theme;

    // Apply the dark class to the HTML element when the theme changes
    // and lazily load theme-specific CSS
    use_effect_with(is_dark_theme, move |is_dark_theme| {
        if let Some(window) = window() {
            if let Some(document) = window.document() {
                if let Some(html_element) = document.document_element() {
                    if *is_dark_theme {
                        let _ = html_element.class_list().add_1("dark");
                        let _ = html_element.class_list().remove_1("light");

                        // Lazily load dark theme CSS if not already loaded
                        let dark_theme_loaded =
                            LocalStorage::get::<bool>(DARK_THEME_LOADED_KEY).unwrap_or(false);
                        if !dark_theme_loaded {
                            load_theme_css(&document, "dark-theme.css", DARK_THEME_LOADED_KEY);
                        }
                    } else {
                        let _ = html_element.class_list().remove_1("dark");
                        let _ = html_element.class_list().add_1("light");

                        // Lazily load light theme CSS if not already loaded
                        let light_theme_loaded =
                            LocalStorage::get::<bool>(LIGHT_THEME_LOADED_KEY).unwrap_or(false);
                        if !light_theme_loaded {
                            load_theme_css(&document, "light-theme.css", LIGHT_THEME_LOADED_KEY);
                        }
                    }
                }
            }
        }

        || {}
    });

    // This component doesn't render anything visible
    html! {}
}

/// Helper function to lazily load theme-specific CSS
fn load_theme_css(document: &Document, css_file: &str, storage_key: &str) {
    log::info!("Lazily loading theme CSS: {}", css_file);

    // Create a new link element for the CSS
    if let Ok(link) = document.create_element("link") {
        // Set attributes using the Element interface
        let _ = link.set_attribute("rel", "stylesheet");
        let _ = link.set_attribute("href", &format!("/assets/css/{}", css_file));

        // Add the link element to the head
        if let Some(head) = document.get_elements_by_tag_name("head").item(0) {
            if let Err(err) = head.append_child(&link) {
                log::error!("Failed to append theme CSS link: {:?}", err);
            } else {
                // Mark the theme CSS as loaded in local storage
                let _ = LocalStorage::set(storage_key, true);
                log::info!("Successfully loaded theme CSS: {}", css_file);
            }
        }
    }
}
