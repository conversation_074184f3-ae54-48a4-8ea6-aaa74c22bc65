use crate::components::SvgIcon;
use crate::router::Route;
use crate::stores::use_style_store;
use crate::ui::Button;
use crate::ui::Tooltip;
use yew::prelude::*;

#[function_component(NavbarButtons)]
pub fn navbar_buttons() -> Html {
    let style_store = use_style_store();
    let is_dark_theme = style_store.is_dark_theme;

    let toggle_theme = Callback::from(move |_| {
        style_store.toggle_dark_theme();
    });

    html! {
        <div class="flex items-center gap-[5px]">
            <Tooltip tooltip="GitHub Repository" position="bottom">
                <Button
                    circle=true
                    variant="text"
                    href="https://github.com/CorentinTh/it-tools"
                    aria_label="GitHub Repository"
                    class="navbar-button h-[34px] w-[34px] flex items-center justify-center"
                >
                    <SvgIcon name="brand-github" size="25" />
                </Button>
            </Tooltip>

            <Tooltip tooltip="Twitter/X" position="bottom">
                <Button
                    circle=true
                    variant="text"
                    href="https://x.com/ittoolsdottech"
                    aria_label="Twitter/X Account"
                    class="navbar-button h-[34px] w-[34px] flex items-center justify-center"
                >
                    <SvgIcon name="brand-x" size="25" />
                </Button>
            </Tooltip>

            <Tooltip tooltip="About" position="bottom">
                <Button
                    circle=true
                    variant="text"
                    to={Route::About}
                    aria_label="About"
                    class="navbar-button h-[34px] w-[34px] flex items-center justify-center"
                >
                    <SvgIcon name="info-circle" size="25" />
                </Button>
            </Tooltip>

            <Tooltip tooltip={if is_dark_theme { "Light Mode" } else { "Dark Mode" }} position="bottom">
                <Button
                    circle=true
                    variant="text"
                    aria_label="Toggle Theme"
                    onclick={toggle_theme}
                    class="theme-toggle-button h-[34px] w-[34px] flex items-center justify-center"
                >
                    if is_dark_theme {
                        <SvgIcon name="sun" size="25" class="text-white" />
                    } else {
                        <SvgIcon name="moon" size="25" class="text-gray-700" />
                    }
                </Button>
            </Tooltip>
        </div>
    }
}
