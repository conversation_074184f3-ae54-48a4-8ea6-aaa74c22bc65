use crate::router::Route;
use crate::tools::{Tool, get_tools};
use crate::ui::InputText;
use crate::ui::Modal;
use gloo::events::EventListener;
use gloo::utils::window;
use wasm_bindgen::JsCast;
use web_sys::{HtmlInputElement, KeyboardEvent};
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Clone, Debug, PartialEq)]
pub struct PaletteOption {
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub to: Option<String>,
    pub href: Option<String>,
    pub keywords: Vec<String>,
}

#[function_component(CommandPalette)]
pub fn command_palette() -> Html {
    let navigator = use_navigator().unwrap();
    let is_modal_open = use_state(|| false);
    let search_prompt = use_state(String::new);
    let input_ref = use_node_ref();
    let selected_index = use_state(|| 0);

    // Detect if user is on Mac for keyboard shortcut display
    let is_mac = use_state(|| {
        // Simple Mac detection - for now just use false, can be enhanced later
        false
    });

    // Get all available tools
    let tools = use_memo((), |_| get_tools());

    // Filter tools based on search query
    let filtered_tools = use_memo((*search_prompt).clone(), |search_query| {
        if search_query.is_empty() {
            (*tools).clone()
        } else {
            let query = search_query.to_lowercase();
            let mut filtered: Vec<(Tool, i32)> = tools
                .iter()
                .filter_map(|tool| {
                    let name_lower = tool.name.to_lowercase();
                    let desc_lower = tool.description.to_lowercase();
                    let cat_lower = tool.category.to_lowercase();

                    // Calculate relevance score (higher = more relevant)
                    let score = if name_lower == query {
                        1000 // Exact name match
                    } else if name_lower.starts_with(&query) {
                        900 // Name starts with query
                    } else if name_lower.contains(&query) {
                        800 // Name contains query
                    } else if desc_lower.contains(&query) {
                        700 // Description contains query
                    } else if cat_lower.contains(&query) {
                        600 // Category contains query
                    } else {
                        return None; // No match
                    };

                    // Debug logging
                    web_sys::console::log_1(&format!("Tool '{}' matches query '{}' with score {} - Name: '{}', Description: '{}', Category: '{}'",
                        tool.name, query, score, tool.name, tool.description, tool.category).into());

                    Some((tool.clone(), score))
                })
                .collect();

            // Sort by score (descending) then by name (ascending) for consistent ordering
            filtered.sort_by(|a, b| b.1.cmp(&a.1).then_with(|| a.0.name.cmp(&b.0.name)));

            let result: Vec<Tool> = filtered.into_iter().map(|(tool, _)| tool).collect();

            web_sys::console::log_1(
                &format!("Search query: '{}', Found {} results", query, result.len()).into(),
            );
            result
        }
    });

    // Reset selected index when search changes
    {
        let selected_index = selected_index.clone();
        use_effect_with((*search_prompt).clone(), move |_| {
            selected_index.set(0);
            || {}
        });
    }

    // Auto-focus the search input when modal opens
    {
        let input_ref = input_ref.clone();
        let is_modal_open = is_modal_open.clone();
        use_effect_with(*is_modal_open, move |is_open| {
            if *is_open {
                // Use a small timeout to ensure the modal is fully rendered
                let input_ref_clone = input_ref.clone();
                gloo::timers::callback::Timeout::new(100, move || {
                    if let Some(input_element) = input_ref_clone.cast::<HtmlInputElement>() {
                        let _ = input_element.focus();
                        // Also select all text if there's any existing content
                        input_element.select();
                    }
                })
                .forget();
            }
            || {}
        });
    }

    // Open modal when Ctrl+K or Cmd+K is pressed and handle navigation
    {
        let is_modal_open = is_modal_open.clone();
        let selected_index = selected_index.clone();
        let filtered_tools = filtered_tools.clone();
        let navigator = navigator.clone();
        let search_prompt = search_prompt.clone();

        use_effect_with((), move |_| {
            let document = window().document().expect("Should have a document");
            let is_modal_open_clone = is_modal_open.clone();
            let selected_index_clone = selected_index.clone();
            let filtered_tools_clone = filtered_tools.clone();
            let navigator_clone = navigator.clone();
            let search_prompt_clone = search_prompt.clone();

            let listener = EventListener::new(&document, "keydown", move |event| {
                let event = event.dyn_ref::<KeyboardEvent>().unwrap();

                // Global shortcut to open modal
                if (event.ctrl_key() || event.meta_key()) && event.key() == "k" {
                    event.prevent_default();
                    is_modal_open_clone.set(true);
                    return;
                }

                // Handle navigation only when modal is open (backup for non-input events)
                if *is_modal_open_clone {
                    match event.key().as_str() {
                        "Escape" => {
                            event.prevent_default();
                            is_modal_open_clone.set(false);
                            search_prompt_clone.set(String::new());
                        }
                        "ArrowDown" => {
                            event.prevent_default();
                            let max_index = filtered_tools_clone.len().saturating_sub(1);
                            let new_index = (*selected_index_clone + 1).min(max_index);
                            selected_index_clone.set(new_index);
                        }
                        "ArrowUp" => {
                            event.prevent_default();
                            let new_index = if *selected_index_clone == 0 {
                                0
                            } else {
                                *selected_index_clone - 1
                            };
                            selected_index_clone.set(new_index);
                        }
                        "Enter" => {
                            event.prevent_default();
                            if !filtered_tools_clone.is_empty()
                                && *selected_index_clone < filtered_tools_clone.len()
                            {
                                let selected_tool = &filtered_tools_clone[*selected_index_clone];
                                // For now, navigate to home page since tool pages aren't implemented yet
                                // TODO: Implement individual tool pages and navigate to selected_tool.path
                                navigator_clone.push(&Route::Home);
                                is_modal_open_clone.set(false);
                                search_prompt_clone.set(String::new());

                                // Log the selected tool for debugging
                                web_sys::console::log_1(
                                    &format!(
                                        "Selected tool: {} ({})",
                                        selected_tool.name, selected_tool.path
                                    )
                                    .into(),
                                );
                            }
                        }
                        _ => {}
                    }
                }
            });

            || {
                drop(listener);
            }
        });
    }

    // Focus input when modal opens
    {
        let input_ref = input_ref.clone();
        let is_modal_open = is_modal_open.clone();
        use_effect_with(*is_modal_open, move |is_open| {
            if *is_open {
                if let Some(input) = input_ref.cast::<HtmlInputElement>() {
                    let _ = input.focus();
                }
            }
            || {}
        });
    }

    let on_open = {
        let is_modal_open = is_modal_open.clone();
        Callback::from(move |_| {
            is_modal_open.set(true);
        })
    };

    let on_close = {
        let is_modal_open = is_modal_open.clone();
        let search_prompt = search_prompt.clone();
        let selected_index = selected_index.clone();
        Callback::from(move |_| {
            is_modal_open.set(false);
            search_prompt.set(String::new());
            selected_index.set(0);
        })
    };

    let on_search_change = {
        let search_prompt = search_prompt.clone();
        Callback::from(move |value: String| {
            search_prompt.set(value);
        })
    };

    // Handle keyboard events specifically for the input field
    let on_keydown = {
        let is_modal_open = is_modal_open.clone();
        let search_prompt = search_prompt.clone();
        let selected_index = selected_index.clone();
        let filtered_tools = filtered_tools.clone();
        let navigator = navigator.clone();

        Callback::from(move |e: KeyboardEvent| {
            web_sys::console::log_1(&format!("Input keydown: {}", e.key()).into());
            match e.key().as_str() {
                "Escape" => {
                    e.prevent_default();
                    web_sys::console::log_1(&"Escape in input - closing modal".into());
                    is_modal_open.set(false);
                    search_prompt.set(String::new());
                    selected_index.set(0);
                }
                "ArrowDown" => {
                    e.prevent_default();
                    let max_index = filtered_tools.len().saturating_sub(1);
                    let new_index = (*selected_index + 1).min(max_index);
                    selected_index.set(new_index);
                }
                "ArrowUp" => {
                    e.prevent_default();
                    let new_index = if *selected_index == 0 {
                        0
                    } else {
                        *selected_index - 1
                    };
                    selected_index.set(new_index);
                }
                "Enter" => {
                    e.prevent_default();
                    if !filtered_tools.is_empty() && *selected_index < filtered_tools.len() {
                        let selected_tool = &filtered_tools[*selected_index];
                        navigator.push(&Route::Home);
                        is_modal_open.set(false);
                        search_prompt.set(String::new());
                        selected_index.set(0);
                        web_sys::console::log_1(
                            &format!(
                                "Selected tool: {} ({})",
                                selected_tool.name, selected_tool.path
                            )
                            .into(),
                        );
                    }
                }
                _ => {}
            }
        })
    };

    let on_tool_click = {
        let navigator = navigator.clone();
        let is_modal_open = is_modal_open.clone();
        let search_prompt = search_prompt.clone();
        let selected_index = selected_index.clone();
        Callback::from(move |tool: Tool| {
            // For now, navigate to home page since tool pages aren't implemented yet
            // TODO: Implement individual tool pages and navigate to tool.path
            navigator.push(&Route::Home);
            is_modal_open.set(false);
            search_prompt.set(String::new());
            selected_index.set(0);

            // Log the selected tool for debugging
            web_sys::console::log_1(&format!("Clicked tool: {} ({})", tool.name, tool.path).into());
        })
    };

    html! {
        <>
            <button
                class="search-bar-button c-button w-full important:justify-start bg-[#e7ebef] dark:bg-[#2e2e2e] rounded-md h-[34px] flex items-center px-[2%] border border-gray-200 dark:border-gray-600 box-border"
                style="--9a468bfe: 14px; --09441655: 34px; --e1cab870: #333639; --645976e7: rgba(46, 51, 56, 0.05); --cea95e96: rgba(46, 51, 56, 0.09); --369e4b1b: rgba(46, 51, 56, 0.22); --c8f65826: #18a058;"
                onclick={on_open}
            >
                <span class="flex items-center justify-between w-full gap-2 h-full text-gray-600 dark:text-gray-300">
                    <div class="flex items-center h-full gap-2">
                        <svg viewBox="0 0 24 24" width="1.2em" height="1.2em" class="flex-shrink-0 text-gray-500 dark:text-gray-400">
                            <path fill="currentColor" d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5l-1.5 1.5l-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16A6.5 6.5 0 0 1 3 9.5A6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14S14 12 14 9.5S12 5 9.5 5Z"></path>
                        </svg>
                        <span class="text-sm font-medium">{"Search"}</span>
                    </div>
                    <span class="border border-gray-400 dark:border-gray-500 rounded px-2 py-1 text-xs font-mono bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-200 flex-shrink-0 inline-flex items-center">
                        {if *is_mac { "⌘" } else { "Ctrl" }}{"+K"}
                    </span>
                </span>
            </button>

            <Modal
                open={*is_modal_open}
                centered={false}
                class="palette-modal max-w-[90vw] sm:max-w-[650px] w-full mx-auto mt-[10vh] mb-auto p-4 max-h-[80vh] overflow-hidden flex flex-col"
                onclose={on_close.clone()}
            >
                // Header with keyboard shortcut hint
                <div class="flex items-center justify-between mb-3 flex-shrink-0">
                    <h2 class="text-lg font-semibold text-primary">{"Search Tools"}</h2>
                    <div class="flex items-center gap-2 text-sm opacity-60">
                        <span>{"Press"}</span>
                        <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono border">
                            {"Esc"}
                        </kbd>
                        <span>{"to close"}</span>
                    </div>
                </div>

                <InputText
                    value={(*search_prompt).clone()}
                    placeholder={Some("Type to search a tool or a command...".to_string())}
                    onchange={on_search_change}
                    onkeydown={on_keydown}
                    node_ref={input_ref}
                    class="search-bar-input text-lg flex-shrink-0"
                    input_class="py-3 px-4 bg-[#e7ebef] dark:bg-[#2e2e2e] rounded-md h-12 w-full"
                />

                <div class="mt-4 flex-1 min-h-0 overflow-hidden flex flex-col w-full">
                    if !filtered_tools.is_empty() {
                        <div class="px-3 py-2 text-sm font-bold text-primary opacity-60 flex-shrink-0">
                            {format!("Tools ({} results)", filtered_tools.len())}
                        </div>

                        <div class="flex-1 overflow-y-auto overflow-x-hidden">
                            { for filtered_tools.iter().enumerate().map(|(index, tool)| {
                                let tool_clone = tool.clone();
                                let on_tool_click = on_tool_click.clone();
                                let is_selected = index == *selected_index;

                                let item_classes = classes!(
                                    "w-full",
                                    "flex",
                                    "cursor-pointer",
                                    "items-center",
                                    "overflow-hidden",
                                    "rounded-md",
                                    "p-3",
                                    "mx-1",
                                    "my-1",
                                    "transition-colors",
                                    "duration-150",
                                    "hover:bg-primary",
                                    "hover:text-white",
                                    if is_selected { "bg-primary text-white" } else { "" }
                                );

                                html! {
                                    <div
                                        key={tool.path.clone()}
                                        class={item_classes}
                                        onclick={
                                            let tool = tool_clone.clone();
                                            Callback::from(move |_| {
                                                on_tool_click.emit(tool.clone());
                                            })
                                        }
                                    >
                                        <div class="flex-1 overflow-hidden">
                                            <div class="truncate font-bold leading-tight opacity-90">
                                                {&tool.name}
                                            </div>
                                            <div class="truncate leading-tight opacity-60">
                                                {&tool.description}
                                            </div>
                                            <div class="truncate text-xs leading-tight opacity-40 mt-1">
                                                {&tool.category}
                                            </div>
                                        </div>
                                    </div>
                                }
                            }) }
                        </div>
                    } else if !search_prompt.is_empty() {
                        <div class="flex-1 flex flex-col items-center justify-center py-8">
                            <div class="text-sm font-bold text-primary opacity-60 mb-2">
                                {"No results found"}
                            </div>
                            <div class="text-sm text-center opacity-60">
                                {format!("No tools found for \"{}\"", *search_prompt)}
                            </div>
                        </div>
                    } else {
                        <div class="flex-1 flex flex-col items-center justify-center py-8">
                            <div class="text-sm font-bold text-primary opacity-60 mb-4">
                                {"Start typing to search tools..."}
                            </div>
                            <div class="text-xs opacity-40 text-center space-y-1">
                                <div class="flex items-center justify-center gap-2">
                                    <kbd class="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono">{"↑↓"}</kbd>
                                    <span>{"Navigate"}</span>
                                </div>
                                <div class="flex items-center justify-center gap-2">
                                    <kbd class="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono">{"Enter"}</kbd>
                                    <span>{"Select"}</span>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </Modal>
        </>
    }
}
