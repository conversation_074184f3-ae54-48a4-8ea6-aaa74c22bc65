use crate::components::{<PERSON><PERSON>utton, SvgIcon};
use crate::router::Route;
use crate::stores::use_style_store;
use crate::tools::Tool;
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ToolCardProps {
    pub tool: Tool,
}

#[function_component(ToolCard)]
pub fn tool_card(props: &ToolCardProps) -> Html {
    let tool = &props.tool;
    let _style_store = use_style_store();
    let theme_primary_color = "#18a058"; // Primary color from theme
    let navigator = use_navigator().unwrap();

    // Get the icon name from the collapsible_tool_menu module
    let icon_name = crate::components::collapsible_tool_menu::get_icon_name_from_tool_info(
        &tool.name,
        &tool.category,
    );

    // Handle tool navigation
    let on_click = {
        let navigator = navigator.clone();
        let tool_path = tool.path.clone();
        Callback::from(move |_: MouseEvent| {
            // Map tool paths to routes
            match tool_path.as_str() {
                "/tools/facebook-video-downloader" => {
                    navigator.push(&Route::FacebookVideoDownloader);
                }
                _ => {
                    // For other tools, navigate to home for now
                    navigator.push(&Route::Home);
                }
            }
        })
    };

    html! {
        <div
            class="tool-card h-full transition transition-duration-500 !border-2 !border-transparent hover:!border-primary cursor-pointer"
            onclick={on_click}
        >
            <div class="tool-card-content">
                <div class="flex items-center justify-between">
                    <SvgIcon name={icon_name} size="40" class={classes!("tool-card-icon")} />

                    <div class="flex items-center gap-2">
                        if tool.is_new {
                            <div
                                class="rounded-full px-2 py-1 text-xs text-white dark:text-neutral-800"
                                style={format!("background-color: {}", theme_primary_color)}
                            >
                                {"NEW"}
                            </div>
                        }

                        <FavoriteButton tool={tool.clone()} />
                    </div>
                </div>

                <div class="my-1 text-lg truncate" style="color: #ffffff;">
                    {&tool.name}
                </div>

                <div class="line-clamp-2 text-neutral-500 dark:text-neutral-400">
                    {&tool.description}
                </div>

                <div class="mt-2 text-xs text-neutral-400">
                    {&tool.category}
                </div>
            </div>
        </div>
    }
}
