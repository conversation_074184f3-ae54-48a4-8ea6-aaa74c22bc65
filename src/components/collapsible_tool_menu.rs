use crate::components::SvgIcon;
use crate::router::Route;
use crate::tools::ToolCategory;
use gloo::storage::{LocalStorage, Storage as GlooStorage};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use yew::prelude::*;
use yew_router::prelude::*;

const STORAGE_KEY: &str = "menu-tool-option:collapsed-categories";

// Function to get the appropriate icon name for a tool based on name and category
pub fn get_icon_name_from_tool_info(name: &str, category: &str) -> String {
    // Determine icon based on tool name or category
    let name_lower = name.to_lowercase();

    // QR code related tools
    if name_lower.contains("qr") || name_lower.contains("qrcode") {
        "qrcode".to_string()
    }
    // ASCII art related tools
    else if name_lower.contains("ascii") {
        return "artboard".to_string();
    }
    // Image related tools
    else if name_lower.contains("image") || name_lower.contains("svg") {
        return "image".to_string();
    }
    // Password related tools
    else if name_lower.contains("password") {
        return "lock".to_string();
    }
    // Base64 related tools
    else if name_lower.contains("base64") {
        return "file-digit".to_string();
    }
    // UUID related tools
    else if name_lower.contains("uuid") {
        return "fingerprint".to_string();
    }
    // ULID related tools
    else if name_lower.contains("ulid") {
        return "sort-descending-numbers".to_string();
    }
    // Text related tools
    else if name_lower.contains("text") {
        return "file-text".to_string();
    }
    // Encryption related tools
    else if name_lower.contains("encrypt")
        || name_lower.contains("decrypt")
        || name_lower.contains("cypher")
    {
        return "lock".to_string();
    }
    // Token related tools
    else if name_lower.contains("token") {
        return "arrows-shuffle".to_string();
    }
    // Hash related tools
    else if name_lower.contains("hash") {
        return "hash".to_string();
    }
    // BIP39 related tools
    else if name_lower.contains("bip39") {
        return "align-justified".to_string();
    }
    // HMAC related tools
    else if name_lower.contains("hmac") {
        return "short-text".to_string();
    }
    // RSA related tools
    else if name_lower.contains("rsa") {
        return "certificate".to_string();
    }
    // JWT related tools
    else if name_lower.contains("jwt") {
        return "key".to_string();
    }
    // Case converter
    else if name_lower.contains("case") && name_lower.contains("convert") {
        return "letter-case-toggle".to_string();
    }
    // Roman numeral converter
    else if name_lower.contains("roman") {
        return "letter-x".to_string();
    }
    // Chronometer
    else if name_lower.contains("chronometer") {
        return "timer".to_string();
    }
    // JSON diff
    else if name_lower.contains("json") && name_lower.contains("diff") {
        return "compare-arrows".to_string();
    }
    // JSON viewer or prettify
    else if name_lower.contains("json")
        && (name_lower.contains("viewer") || name_lower.contains("prettify"))
    {
        return "braces".to_string();
    }
    // YAML viewer or prettify
    else if name_lower.contains("yaml")
        && (name_lower.contains("viewer") || name_lower.contains("prettify"))
    {
        return "align-justified".to_string();
    }
    // Crontab generator
    else if name_lower.contains("crontab") {
        return "alarm".to_string();
    }
    // Basic auth generator
    else if name_lower.contains("basic") && name_lower.contains("auth") {
        return "password".to_string();
    }
    // String obfuscator
    else if name_lower.contains("string") && name_lower.contains("obfuscator") {
        return "eye-off".to_string();
    }
    // Benchmark builder
    else if name_lower.contains("benchmark") {
        return "speed".to_string();
    }
    // Default based on category
    else {
        match category {
            "Crypto" => "lock".to_string(),
            "Converter" => "refresh".to_string(),
            "Web" => "world".to_string(),
            "Generator" => "zap".to_string(),
            "Text" => "file-text".to_string(),
            "Code" => "code".to_string(),
            "Development" => "code".to_string(),
            "Network" => "world".to_string(),
            "Math" => "plus".to_string(),
            "Measurement" => "adjustments".to_string(),
            "Images and videos" => "image".to_string(),
            "Data" => "database".to_string(),
            _ => "cube".to_string(), // Default icon
        }
    }
}

// Function to render the tool icon
fn get_tool_icon(tool: &crate::tools::Tool) -> Html {
    let icon_name = get_icon_name_from_tool_info(&tool.name, &tool.category);

    // Check if the icon is from Material Design (they have different styling)
    let is_material_icon = matches!(
        icon_name.as_str(),
        "image_outlined"
            | "timer_outlined"
            | "speed_filled"
            | "compare_arrows_round"
            | "short_text_round"
            | "http_round"
            | "password_round"
    );

    if is_material_icon {
        html! {
            <SvgIcon name={icon_name} size="18" stroke_width="0" fill="#e9e9e9" class="sidebar-icon" />
        }
    } else {
        html! {
            <SvgIcon name={icon_name} size="16" stroke_width="1.5" class="sidebar-icon" />
        }
    }
}

#[derive(Properties, PartialEq)]
pub struct CollapsibleToolMenuProps {
    pub tools_by_category: Vec<ToolCategory>,
}

#[derive(Clone, Serialize, Deserialize)]
struct CollapsedState {
    categories: HashMap<String, bool>,
}

#[function_component(CollapsibleToolMenu)]
pub fn collapsible_tool_menu(props: &CollapsibleToolMenuProps) -> Html {
    // State to track which categories are collapsed
    let collapsed_categories = use_state(|| {
        // Try to load from localStorage
        match LocalStorage::get::<CollapsedState>(STORAGE_KEY) {
            Ok(state) => state.categories,
            Err(_) => {
                // Initialize with default values
                let mut map = HashMap::new();
                for category in &props.tools_by_category {
                    map.insert(category.name.clone(), false); // Default to expanded
                }
                map
            }
        }
    });

    // Save to localStorage when state changes
    {
        let collapsed_categories_clone = collapsed_categories.clone();
        use_effect_with(collapsed_categories.clone(), move |_| {
            let state = CollapsedState {
                categories: (*collapsed_categories_clone).clone(),
            };
            let _ = LocalStorage::set(STORAGE_KEY, &state);
            || ()
        });
    }

    // Toggle category collapse state
    let toggle_category = {
        let collapsed_categories = collapsed_categories.clone();
        Callback::from(move |category_name: String| {
            let mut new_map = (*collapsed_categories).clone();
            let current = *new_map.get(&category_name).unwrap_or(&false);
            new_map.insert(category_name, !current);
            collapsed_categories.set(new_map);
        })
    };

    html! {
        <div>
            {
                props.tools_by_category.iter().map(|category| {
                    let is_collapsed = *collapsed_categories.get(&category.name).unwrap_or(&false);
                    let category_name = category.name.clone();
                    let toggle = {
                        let category_name = category_name.clone();
                        let toggle_category = toggle_category.clone();
                        Callback::from(move |_| {
                            toggle_category.emit(category_name.clone());
                        })
                    };

                    html! {
                        <div key={category.name.clone()} class="category-container mb-2">
                            <div class="category-header ml-[6px] mt-3 flex cursor-pointer items-center opacity-60" onclick={toggle.clone()}>
                                <span class={classes!(
                                    "chevron", "text-base", "leading-none", "opacity-50", "transition-transform",
                                    is_collapsed.then_some("rotate-0"),
                                    (!is_collapsed).then_some("rotate-90")
                                )}>
                                    <SvgIcon name="chevron-right" size="16" stroke_width="2" class="sidebar-icon" />
                                </span>
                                <span class="category-name ml-2 text-sm">{&category.name}</span>
                            </div>

                            <div class={classes!(
                                "menu-wrapper", "flex", "flex-row",
                                is_collapsed.then_some("hidden")
                            )}>
                                <div class="toggle-bar w-6 opacity-10 transition-opacity duration-200 relative cursor-pointer hover:opacity-50 before:w-0.5 before:h-full before:content-[''] before:bg-text-muted-light dark:before:bg-text-muted-dark before:rounded before:absolute before:top-0 before:left-[14px]" onclick={toggle}></div>
                                <div class="tool-list flex-1 flex flex-col mb-[5px] w-full">
                                    {
                                        category.tools.iter().map(|tool| {
                                            let _tool_path = tool.path.clone();
                                            let tool_name = tool.name.clone();
                                            html! {
                                                <Link<Route> to={Route::Home} classes="tool-link">
                                                    <div class="tool-item py-2 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                                                        <div class="flex items-center w-full">
                                                            <div class="menu-icon-item flex-shrink-0 mr-2 relative">
                                                                { get_tool_icon(tool) }
                                                                if tool.is_new {
                                                                    <span class="badge absolute w-[6px] h-[6px] bg-primary rounded-full top-[3px] left-[-6px]"></span>
                                                                }
                                                            </div>
                                                            <div class="overflow-hidden whitespace-nowrap text-ellipsis w-full">
                                                                <span class="tool-name text-sm" title={tool_name.clone()}>{tool_name}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Link<Route>>
                                            }
                                        }).collect::<Html>()
                                    }
                                </div>
                            </div>
                        </div>
                    }
                }).collect::<Html>()
            }
        </div>
    }
}
