use crate::stores::use_style_store;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct MenuLayoutProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub sider: Html,
    #[prop_or_default]
    pub content: Html,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(MenuLayout)]
pub fn menu_layout(props: &MenuLayoutProps) -> Html {
    let style_store = use_style_store();
    let is_menu_collapsed = style_store.is_menu_collapsed;
    let is_small_screen = style_store.is_small_screen;

    // Create a callback to close the menu when overlay is clicked
    let toggle_menu = {
        let style_store = style_store.clone();
        Callback::from(move |_| {
            if is_small_screen {
                style_store.toggle_menu_collapsed();
            }
        })
    };

    html! {
        <div class={classes!(
            "menu-layout-container",
            is_menu_collapsed.then_some("menu-collapsed"),
            props.class.clone()
        )}>
            <div class="sider-container">
                { props.sider.clone() }
            </div>
            <div class="content-container">
                { props.content.clone() }
                <div class="main-content">
                    { for props.children.iter() }
                </div>
                if is_small_screen && !is_menu_collapsed {
                    <div class="menu-overlay" onclick={toggle_menu}></div>
                }
            </div>
        </div>
    }
}
