use crate::components::SvgIcon;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ColoredCardProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub title: String,
    #[prop_or_default]
    pub icon: String,
}

#[function_component(ColoredCard)]
pub fn colored_card(props: &ColoredCardProps) -> Html {
    html! {
        <div class="tool-card colored-card h-full">
            <div class="tool-card-content">
                <div class="flex items-center justify-between">
                    <SvgIcon name={props.icon.clone()} size="40" class={classes!("text-white", "opacity-70")} />
                </div>
                <div class="my-1 text-lg text-white">
                    {&props.title}
                </div>
                <div class="text-white opacity-80">
                    { for props.children.iter() }
                </div>
            </div>
        </div>
    }
}
