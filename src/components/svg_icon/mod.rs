mod material_icons;
mod tabler_icons;

use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct SvgIconProps {
    /// The name of the icon to display
    pub name: String,
    /// Optional size of the icon (default: "24")
    #[prop_or("24".to_string())]
    pub size: String,
    /// Optional CSS class to apply to the icon
    #[prop_or_default]
    pub class: Classes,
    /// Optional stroke width (default: "2")
    #[prop_or("2".to_string())]
    pub stroke_width: String,
    /// Optional fill color (default: "none")
    #[prop_or("none".to_string())]
    pub fill: String,
}

/// SvgIcon component that renders SVG icons directly in the DOM
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::components::SvgIcon;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <SvgIcon name="heart" size="20" />
///     }
/// }
/// ```
#[function_component(SvgIcon)]
pub fn svg_icon(props: &SvgIconProps) -> Html {
    // Common SVG attributes for all icons

    // First check if it's a Tabler icon
    if let Some(icon_content) = tabler_icons::get_icon(&props.name) {
        return html! {
            <span class="svg-icon-wrapper" data-icon={props.name.clone()}>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width={props.size.clone()}
                    height={props.size.clone()}
                    viewBox="0 0 24 24"
                    fill={props.fill.clone()}
                    stroke="currentColor"
                    stroke-width={props.stroke_width.clone()}
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class={classes!("svg-icon", props.class.clone())}
                >
                    {icon_content}
                </svg>
            </span>
        };
    }

    // Then check if it's a Material icon
    if let Some(icon_content) = material_icons::get_icon(&props.name) {
        return html! {
            <span class="svg-icon-wrapper" data-icon={props.name.clone()}>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width={props.size.clone()}
                    height={props.size.clone()}
                    viewBox="0 0 24 24"
                    fill={props.fill.clone()}
                    stroke="currentColor"
                    stroke-width={props.stroke_width.clone()}
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class={classes!("svg-icon", props.class.clone())}
                >
                    {icon_content}
                </svg>
            </span>
        };
    }

    // We only use SVG icons from our libraries

    // Default fallback icon (question mark)
    html! {
        <span class="svg-icon-wrapper" data-icon={props.name.clone()}>
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={props.size.clone()}
                height={props.size.clone()}
                viewBox="0 0 24 24"
                fill={props.fill.clone()}
                stroke="currentColor"
                stroke-width={props.stroke_width.clone()}
                stroke-linecap="round"
                stroke-linejoin="round"
                class={classes!("svg-icon", props.class.clone())}
            >
                <circle cx="12" cy="12" r="10"/>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                <path d="M12 17h.01"/>
            </svg>
        </span>
    }
}
