use yew::prelude::*;

/// Returns the SVG content for a Tabler icon by name
pub fn get_icon(name: &str) -> Option<Html> {
    match name {
        "heart" => Some(html! {
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/>
        }),
        "sun" => Some(html! {
            <>
                <circle cx="12" cy="12" r="4"/>
                <path d="M12 2v2"/>
                <path d="M12 20v2"/>
                <path d="m4.93 4.93 1.41 1.41"/>
                <path d="m17.66 17.66 1.41 1.41"/>
                <path d="M2 12h2"/>
                <path d="M20 12h2"/>
                <path d="m6.34 17.66-1.41 1.41"/>
                <path d="m19.07 4.93-1.41 1.41"/>
            </>
        }),
        "moon" => Some(html! {
            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
        }),
        "github" => Some(html! {
            <>
                <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"/>
                <path d="M9 18c-4.51 2-5-2-7-2"/>
            </>
        }),
        "chevron-right" => Some(html! {
            <path d="m9 18 6-6-6-6"/>
        }),
        "chevron-down" => Some(html! {
            <path d="m6 9 6 6 6-6"/>
        }),
        "menu" | "menu-2" => Some(html! {
            <>
                <line x1="4" y1="12" x2="20" y2="12"/>
                <line x1="4" y1="6" x2="20" y2="6"/>
                <line x1="4" y1="18" x2="20" y2="18"/>
            </>
        }),
        "home" | "home-2" => Some(html! {
            <>
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9 22 9 12 15 12 15 22"/>
            </>
        }),
        "copy" => Some(html! {
            <>
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
            </>
        }),
        "eye" => Some(html! {
            <>
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/>
                <circle cx="12" cy="12" r="3"/>
            </>
        }),
        "eye-off" => Some(html! {
            <>
                <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/>
                <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/>
                <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/>
                <line x1="2" x2="22" y1="2" y2="22"/>
            </>
        }),
        "search" => Some(html! {
            <>
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.3-4.3"/>
            </>
        }),
        "close" | "x" => Some(html! {
            <>
                <path d="M18 6 6 18"/>
                <path d="m6 6 12 12"/>
            </>
        }),
        "file-text" => Some(html! {
            <>
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                <polyline points="14 2 14 8 20 8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <line x1="10" y1="9" x2="8" y2="9"/>
            </>
        }),
        "qrcode" => Some(html! {
            <>
                <rect width="5" height="5" x="3" y="3" rx="1"/>
                <rect width="5" height="5" x="16" y="3" rx="1"/>
                <rect width="5" height="5" x="3" y="16" rx="1"/>
                <path d="M21 16h-3a2 2 0 0 0-2 2v3"/>
                <path d="M21 21v.01"/>
                <path d="M12 7v3a2 2 0 0 1-2 2H7"/>
                <path d="M3 12h.01"/>
                <path d="M12 3h.01"/>
                <path d="M12 16v.01"/>
                <path d="M16 12h1"/>
                <path d="M21 12v.01"/>
                <path d="M12 21v-1"/>
            </>
        }),
        "world" | "globe" => Some(html! {
            <>
                <circle cx="12" cy="12" r="10"/>
                <line x1="2" y1="12" x2="22" y2="12"/>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
            </>
        }),
        "device-mobile" => Some(html! {
            <>
                <rect width="14" height="20" x="5" y="2" rx="2" ry="2"/>
                <line x1="12" y1="18" x2="12.01" y2="18"/>
            </>
        }),
        "file-digit" => Some(html! {
            <>
                <path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4"/>
                <polyline points="14 2 14 8 20 8"/>
                <path d="M10 12h2v6"/>
                <rect width="4" height="6" x="2" y="12" rx="1"/>
            </>
        }),
        "artboard" => Some(html! {
            <>
                <rect width="16" height="16" x="4" y="4" rx="2"/>
                <line x1="4" y1="12" x2="20" y2="12"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
            </>
        }),
        "image" => Some(html! {
            <>
                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                <circle cx="9" cy="9" r="2"/>
                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
            </>
        }),
        "sort-descending" => Some(html! {
            <>
                <path d="M11 5h10"/>
                <path d="M11 9h7"/>
                <path d="M11 13h4"/>
                <path d="m3 17 3 3 3-3"/>
                <path d="M6 18V4"/>
            </>
        }),
        "sort-descending-numbers" => Some(html! {
            <>
                <path d="M4 15h3v4l2-2"/>
                <path d="M7 15V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v9"/>
                <path d="M11 6h10"/>
                <path d="M11 10h7"/>
                <path d="M11 14h4"/>
            </>
        }),
        "align-justified" => Some(html! {
            <>
                <line x1="3" y1="6" x2="21" y2="6"/>
                <line x1="3" y1="12" x2="21" y2="12"/>
                <line x1="3" y1="18" x2="21" y2="18"/>
            </>
        }),
        "lock" => Some(html! {
            <>
                <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
            </>
        }),
        "refresh" => Some(html! {
            <>
                <path d="M3 2v6h6"/>
                <path d="M21 12A9 9 0 0 0 6 5.3L3 8"/>
                <path d="M21 22v-6h-6"/>
                <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"/>
            </>
        }),
        "zap" => Some(html! {
            <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/>
        }),
        "cube" => Some(html! {
            <>
                <path d="m21 16-9 5-9-5V8l9-5 9 5v8z"/>
                <path d="M12 21v-8"/>
                <path d="m12 13 9-5"/>
                <path d="m12 13-9-5"/>
            </>
        }),
        "code" => Some(html! {
            <>
                <polyline points="16 18 22 12 16 6"/>
                <polyline points="8 6 2 12 8 18"/>
            </>
        }),
        "code-plus" => Some(html! {
            <>
                <path d="m9 12h6"/>
                <path d="m12 9v6"/>
                <path d="M8 9h8"/>
                <path d="M8 15h8"/>
                <path d="M8 5h8"/>
                <path d="M8 19h8"/>
            </>
        }),
        "brand-github" => Some(html! {
            <>
                <path d="M9 19c-4.3 1.4-4.3-2.5-6-3m12 5v-3.5c0-1 .1-1.4-.5-2 2.8-.3 5.5-1.4 5.5-6a4.6 4.6 0 0 0-1.3-3.2 4.2 4.2 0 0 0-.1-3.2s-1.1-.3-3.5 1.3a12.3 12.3 0 0 0-6.2 0C6.5 2.8 5.4 3.1 5.4 3.1a4.2 4.2 0 0 0-.1 3.2A4.6 4.6 0 0 0 4 9.5c0 4.6 2.7 5.7 5.5 6-.6.6-.6 1.2-.5 2V21"/>
            </>
        }),
        "brand-x" => Some(html! {
            <>
                <path d="M4 4l11.733 16h4.267"/>
                <path d="M4 20l6.768-6.768m2.46-2.46L20 4"/>
            </>
        }),
        "blockquote" => Some(html! {
            <>
                <path d="M6 15h15"/>
                <path d="M21 19h-15"/>
                <path d="M15 11h6"/>
                <path d="M21 7h-6"/>
                <path d="M9 9h1a1 1 0 1 1-1 1v-2.5a2 2 0 0 1 2-2"/>
                <path d="M3 9h1a1 1 0 1 1-1 1v-2.5a2 2 0 0 1 2-2"/>
            </>
        }),
        "bold" => Some(html! {
            <>
                <path d="M7 5h6a3.5 3.5 0 0 1 0 7h-6z"/>
                <path d="M13 12h1a3.5 3.5 0 0 1 0 7h-7v-7"/>
            </>
        }),
        "clear-formatting" => Some(html! {
            <>
                <path d="M17 15l4 4m0-4l-4 4"/>
                <path d="M7 6v-1h11v1"/>
                <path d="M7 19h4"/>
                <path d="M13 5l-4 14"/>
            </>
        }),
        "h1" => Some(html! {
            <>
                <path d="M19 18v-8l-2 2"/>
                <path d="M4 6v12"/>
                <path d="M12 6v12"/>
                <path d="M11 18h2"/>
                <path d="M3 18h2"/>
                <path d="M4 12h8"/>
            </>
        }),
        "h2" => Some(html! {
            <>
                <path d="M17 12a2 2 0 1 1 4 0c0 .591-.417 1.318-.816 1.858L17 18.001h4"/>
                <path d="M4 6v12"/>
                <path d="M12 6v12"/>
                <path d="M11 18h2"/>
                <path d="M3 18h2"/>
                <path d="M4 12h8"/>
            </>
        }),
        "h3" => Some(html! {
            <>
                <path d="M19 14a2 2 0 1 0-2-2"/>
                <path d="M17 16a2 2 0 1 0 2-2"/>
                <path d="M4 6v12"/>
                <path d="M12 6v12"/>
                <path d="M11 18h2"/>
                <path d="M3 18h2"/>
                <path d="M4 12h8"/>
            </>
        }),
        "h4" => Some(html! {
            <>
                <path d="M20 18v-8l-4 6h5"/>
                <path d="M4 6v12"/>
                <path d="M12 6v12"/>
                <path d="M11 18h2"/>
                <path d="M3 18h2"/>
                <path d="M4 12h8"/>
            </>
        }),
        "italic" => Some(html! {
            <>
                <line x1="11" y1="5" x2="17" y2="5"/>
                <line x1="7" y1="19" x2="13" y2="19"/>
                <line x1="14" y1="5" x2="10" y2="19"/>
            </>
        }),
        "list" => Some(html! {
            <>
                <line x1="9" y1="6" x2="20" y2="6"/>
                <line x1="9" y1="12" x2="20" y2="12"/>
                <line x1="9" y1="18" x2="20" y2="18"/>
                <line x1="5" y1="6" x2="5" y2="6.01"/>
                <line x1="5" y1="12" x2="5" y2="12.01"/>
                <line x1="5" y1="18" x2="5" y2="18.01"/>
            </>
        }),
        "list-numbers" => Some(html! {
            <>
                <path d="M11 6h9"/>
                <path d="M11 12h9"/>
                <path d="M11 18h9"/>
                <path d="M4 16a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1z"/>
                <path d="M4 10a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1z"/>
                <path d="M4 4a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1z"/>
            </>
        }),
        "strikethrough" => Some(html! {
            <>
                <path d="M5 12h14"/>
                <path d="M16 6.5a4 2 0 0 0-4-1.5h-1a3.5 3.5 0 0 0 0 7h2a3.5 3.5 0 0 1 0 7h-1.5a4 2 0 0 1-4-1.5"/>
            </>
        }),
        "text-wrap" => Some(html! {
            <>
                <line x1="4" y1="6" x2="20" y2="6"/>
                <line x1="4" y1="18" x2="9" y2="18"/>
                <path d="M4 12h13a3 3 0 0 1 0 6h-4l2-2m0 4l-2-2"/>
            </>
        }),
        "arrow-back" => Some(html! {
            <>
                <path d="M9 11l-4 4l4 4m-4-4h11a4 4 0 0 0 0-8h-1"/>
            </>
        }),
        "arrow-forward-up" => Some(html! {
            <>
                <path d="M15 13l4-4l-4-4"/>
                <path d="M19 9h-8a4 4 0 0 0-4 4v3"/>
            </>
        }),
        "arrow-left" => Some(html! {
            <>
                <line x1="19" y1="12" x2="5" y2="12"/>
                <polyline points="12 19 5 12 12 5"/>
            </>
        }),
        "arrow-right" => Some(html! {
            <>
                <line x1="5" y1="12" x2="19" y2="12"/>
                <polyline points="12 5 19 12 12 19"/>
            </>
        }),
        "exchange" => Some(html! {
            <>
                <circle cx="5" cy="18" r="2"/>
                <circle cx="19" cy="6" r="2"/>
                <path d="M19 4v2"/>
                <path d="M5 20v2"/>
                <path d="M7 18h10a2 2 0 0 0 2-2V8"/>
                <path d="M17 6H7a2 2 0 0 0-2 2v8"/>
            </>
        }),
        "plus" => Some(html! {
            <>
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
            </>
        }),
        "trash" => Some(html! {
            <>
                <path d="M3 6h18"/>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
            </>
        }),
        "adjustments" => Some(html! {
            <>
                <circle cx="6" cy="10" r="2"/>
                <line x1="6" y1="4" x2="6" y2="8"/>
                <line x1="6" y1="12" x2="6" y2="20"/>
                <circle cx="12" cy="16" r="2"/>
                <line x1="12" y1="4" x2="12" y2="14"/>
                <line x1="12" y1="18" x2="12" y2="20"/>
                <circle cx="18" cy="7" r="2"/>
                <line x1="18" y1="4" x2="18" y2="5"/>
                <line x1="18" y1="9" x2="18" y2="20"/>
            </>
        }),
        "browser" => Some(html! {
            <>
                <rect width="20" height="16" x="2" y="4" rx="2"/>
                <line x1="2" y1="10" x2="22" y2="10"/>
            </>
        }),
        "cpu" => Some(html! {
            <>
                <rect x="4" y="4" width="16" height="16" rx="2"/>
                <rect x="9" y="9" width="6" height="6"/>
                <path d="M15 2v2"/>
                <path d="M15 20v2"/>
                <path d="M2 15h2"/>
                <path d="M2 9h2"/>
                <path d="M20 15h2"/>
                <path d="M20 9h2"/>
                <path d="M9 2v2"/>
                <path d="M9 20v2"/>
            </>
        }),
        "devices" => Some(html! {
            <>
                <rect width="14" height="14" x="8" y="4" rx="2"/>
                <path d="M4 8V6a2 2 0 0 1 2-2h2"/>
                <path d="M4 16v2a2 2 0 0 0 2 2h2"/>
            </>
        }),
        "engine" => Some(html! {
            <>
                <path d="M3 10h8"/>
                <path d="M8 10V6"/>
                <path d="M12 6h4v4"/>
                <path d="M12 14h4v4"/>
                <path d="M3 14h8"/>
                <path d="M8 14v4"/>
                <circle cx="18" cy="18" r="2"/>
                <circle cx="18" cy="6" r="2"/>
            </>
        }),
        "key" => Some(html! {
            <>
                <path d="M16.2 11c1.8 0 3.2-1.4 3.2-3.2C19.4 6 18 4.6 16.2 4.6c-1.8 0-3.2 1.4-3.2 3.2 0 .******* 1l-8.6 8.6L4 20l2.6-.4 1.4-1.4"/>
                <path d="M15 9 9 15"/>
            </>
        }),
        "hash" => Some(html! {
            <>
                <line x1="4" y1="9" x2="20" y2="9"/>
                <line x1="4" y1="15" x2="20" y2="15"/>
                <line x1="10" y1="3" x2="8" y2="21"/>
                <line x1="16" y1="3" x2="14" y2="21"/>
            </>
        }),
        "database" => Some(html! {
            <>
                <ellipse cx="12" cy="6" rx="8" ry="3"/>
                <path d="M4 6v6a8 3 0 0 0 16 0V6"/>
                <path d="M4 12v6a8 3 0 0 0 16 0v-6"/>
            </>
        }),
        "fingerprint" => Some(html! {
            <>
                <path d="M18.9 7a8 8 0 0 1 1.1 5v1a6 6 0 0 0 .8 3"/>
                <path d="M8 11a4 4 0 0 1 8 0v1a10 10 0 0 0 2 6"/>
                <path d="M12 11v2a14 14 0 0 0 2.5 8"/>
                <path d="M8 15a18 18 0 0 0 1.8 6"/>
                <path d="M4.9 19a22 22 0 0 1-.9-7v-1a8 8 0 0 1 12-6.95"/>
            </>
        }),
        "arrows-shuffle" => Some(html! {
            <>
                <path d="M18 4l3 3l-3 3"/>
                <path d="M18 20l3-3l-3-3"/>
                <path d="M3 7h3a5 5 0 0 1 5 5a5 5 0 0 0 5 5h5"/>
                <path d="M3 17h3a5 5 0 0 0 5-5a5 5 0 0 1 5-5h5"/>
            </>
        }),
        "certificate" => Some(html! {
            <>
                <path d="M15 15m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"/>
                <path d="M13 17.5v4.5l2 -1.5l2 1.5v-4.5"/>
                <path d="M10 19h-5a2 2 0 0 1 -2 -2v-10c0 -1.1 .9 -2 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -1 1.73"/>
                <path d="M6 9l12 0"/>
                <path d="M6 12l3 0"/>
                <path d="M6 15l2 0"/>
            </>
        }),
        "letter-case-toggle" => Some(html! {
            <>
                <path d="M6.5 15.5m-3.5 0a3.5 3.5 0 1 0 7 0a3.5 3.5 0 1 0 -7 0"/>
                <path d="M14 19v-10.5a3.5 3.5 0 0 1 7 0v10.5"/>
                <path d="M14 13h7"/>
                <path d="M10 12v7"/>
            </>
        }),
        "letter-x" => Some(html! {
            <>
                <path d="M7 4l10 16"/>
                <path d="M17 4l-10 16"/>
            </>
        }),
        "timer" => Some(html! {
            <>
                <path d="M10 2h4"/>
                <path d="M12 14l.001 -5"/>
                <path d="M12 14m-8 0a8 8 0 1 0 16 0a8 8 0 1 0 -16 0"/>
            </>
        }),
        "compare-arrows" => Some(html! {
            <>
                <path d="M21 12l-8 -4.5v9z"/>
                <path d="M3 12l8 4.5v-9z"/>
                <path d="M12 12h0"/>
            </>
        }),
        "braces" => Some(html! {
            <>
                <path d="M7 4a2 2 0 0 0 -2 2v3a2 3 0 0 1 -2 3a2 3 0 0 1 2 3v3a2 2 0 0 0 2 2"/>
                <path d="M17 4a2 2 0 0 1 2 2v3a2 3 0 0 0 2 3a2 3 0 0 0 -2 3v3a2 2 0 0 1 -2 2"/>
            </>
        }),
        "alarm" => Some(html! {
            <>
                <path d="M12 13m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"/>
                <path d="M12 10l0 3l2 0"/>
                <path d="M7 4l-2.75 2"/>
                <path d="M17 4l2.75 2"/>
            </>
        }),
        "password" => Some(html! {
            <>
                <path d="M12 10v4"/>
                <path d="M10 13l4 -2"/>
                <path d="M10 11l4 2"/>
                <path d="M5 10v4"/>
                <path d="M3 13l4 -2"/>
                <path d="M3 11l4 2"/>
                <path d="M19 10v4"/>
                <path d="M17 13l4 -2"/>
                <path d="M17 11l4 2"/>
            </>
        }),
        "short-text" => Some(html! {
            <>
                <path d="M4 9h8"/>
                <path d="M4 15h4"/>
            </>
        }),
        "speed" => Some(html! {
            <>
                <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/>
                <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/>
                <path d="M12 14l1.5 -1.5"/>
                <path d="M20 12h-2"/>
                <path d="M4 12h2"/>
                <path d="M12 4v2"/>
                <path d="M12 18v2"/>
            </>
        }),
        _ => None,
    }
}
