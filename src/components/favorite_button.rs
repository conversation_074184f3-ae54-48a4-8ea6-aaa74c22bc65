use crate::components::SvgIcon;
use crate::stores::use_style_store;
use crate::tools::Tool;
use gloo::storage::{LocalStorage, Storage};
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct FavoriteButtonProps {
    pub tool: Tool,
}

const FAVORITE_TOOLS_KEY: &str = "favoriteToolsName";

#[function_component(FavoriteButton)]
pub fn favorite_button(props: &FavoriteButtonProps) -> Html {
    let _style_store = use_style_store();

    let is_favorite = use_state(|| {
        let favorites: Vec<String> =
            LocalStorage::get(FAVORITE_TOOLS_KEY).unwrap_or_else(|_| Vec::new());
        favorites.contains(&props.tool.name)
    });

    let tool_name = props.tool.name.clone();
    let onclick = {
        let is_favorite = is_favorite.clone();
        Callback::from(move |e: MouseEvent| {
            e.prevent_default();
            e.stop_propagation();
            let mut favorites: Vec<String> =
                LocalStorage::get(FAVORITE_TOOLS_KEY).unwrap_or_else(|_| Vec::new());

            if *is_favorite {
                favorites.retain(|name| name != &tool_name);
            } else {
                favorites.push(tool_name.clone());
            }

            let _ = LocalStorage::set(FAVORITE_TOOLS_KEY, favorites);
            is_favorite.set(!*is_favorite);
        })
    };

    let button_type = if *is_favorite { "primary" } else { "default" };
    let opacity_style = if *is_favorite {
        "opacity: 1"
    } else {
        "opacity: 0.2"
    };

    let tooltip_text = if *is_favorite {
        "Remove from favorites"
    } else {
        "Add to favorites"
    };

    html! {
        <div class="tooltip-wrapper" title={tooltip_text}>
            <button
                class={classes!(
                    "btn",
                    "btn-text",
                    "btn-circle",
                    format!("btn-type-{}", button_type)
                )}
                style={opacity_style}
                onclick={onclick}
                aria-label={tooltip_text}
            >
                <SvgIcon name="heart" size="24" />
            </button>
        </div>
    }
}
