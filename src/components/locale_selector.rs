use crate::ui::Select;
use crate::ui::SelectOption;
use gloo::storage::{LocalStorage, Storage};
use yew::prelude::*;

const LOCALE_KEY: &str = "locale";

#[derive(Properties, PartialEq)]
pub struct LocaleSelectorProps {
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(LocaleSelector)]
pub fn locale_selector(props: &LocaleSelectorProps) -> Html {
    // Get current locale from local storage or default to "en"
    let current_locale =
        use_state(|| LocalStorage::get(LOCALE_KEY).unwrap_or_else(|_| "en".to_string()));

    // Available locales with their display names
    let locales = vec![
        SelectOption {
            label: "English".to_string(),
            value: "en".to_string(),
            tooltip: None,
        },
        SelectOption {
            label: "Français".to_string(),
            value: "fr".to_string(),
            tooltip: None,
        },
        SelectOption {
            label: "Español".to_string(),
            value: "es".to_string(),
            tooltip: None,
        },
        SelectOption {
            label: "Deutsch".to_string(),
            value: "de".to_string(),
            tooltip: None,
        },
        SelectOption {
            label: "中文".to_string(),
            value: "zh".to_string(),
            tooltip: None,
        },
    ];

    let on_locale_change = {
        let current_locale = current_locale.clone();
        Callback::from(move |value: String| {
            // Save to local storage
            let _ = LocalStorage::set(LOCALE_KEY, value.clone());
            current_locale.set(value);
        })
    };

    html! {
        <Select
            value={Some((*current_locale).clone())}
            options={locales}
            placeholder="Select language"
            class={classes!("locale-selector", "w-full", "h-[34px]", props.class.clone())}
            onchange={on_locale_change}
        />
    }
}
