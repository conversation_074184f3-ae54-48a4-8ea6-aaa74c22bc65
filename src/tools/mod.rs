pub mod facebook_video_downloader;

/// Represents a tool in the application
///
/// Each tool has a name, path, description, category, and various flags
#[derive(<PERSON>lone, PartialEq, Debug, serde::Serialize, serde::Deserialize)]
pub struct Tool {
    /// The name of the tool
    pub name: String,
    /// The path to the tool in the application
    pub path: String,
    /// A description of what the tool does
    pub description: String,
    /// The category the tool belongs to
    pub category: String,
    /// Flag indicating if this is a new tool
    pub is_new: bool,
    /// Flag indicating if this tool is favorited by the user
    #[serde(default)]
    pub is_favorite: bool,
    /// Keywords for search functionality
    #[serde(default)]
    pub keywords: Vec<String>,
}

/// Represents a category of tools
///
/// Each category has a name and contains a list of tools
#[derive(Clone, PartialEq, Debug)]
pub struct ToolCategory {
    /// The name of the category
    pub name: String,
    /// The list of tools in this category
    pub tools: Vec<Tool>,
}

/// Returns a list of all available tools in the application
///
/// This function creates and returns a vector of all tools available in the application
pub fn get_tools() -> Vec<Tool> {
    vec![
        Tool {
            name: "Token generator".to_string(),
            path: "/tools/token-generator".to_string(),
            description: "Generate random tokens with custom length and character set".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "token".to_string(),
                "random".to_string(),
                "generator".to_string(),
            ],
        },
        Tool {
            name: "Hash text".to_string(),
            path: "/tools/hash-text".to_string(),
            description: "Hash text using various algorithms".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec!["hash".to_string(), "md5".to_string(), "sha".to_string()],
        },
        Tool {
            name: "Bcrypt".to_string(),
            path: "/tools/bcrypt".to_string(),
            description: "Hash and verify passwords using bcrypt".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "bcrypt".to_string(),
                "password".to_string(),
                "hash".to_string(),
            ],
        },
        Tool {
            name: "UUIDs generator".to_string(),
            path: "/tools/uuid-generator".to_string(),
            description: "Generate random UUIDs".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "uuid".to_string(),
                "guid".to_string(),
                "identifier".to_string(),
            ],
        },
        Tool {
            name: "ULID generator".to_string(),
            path: "/tools/ulid-generator".to_string(),
            description:
                "Generate ULIDs (Universally Unique Lexicographically Sortable Identifiers)"
                    .to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "ulid".to_string(),
                "identifier".to_string(),
                "sortable".to_string(),
            ],
        },
        Tool {
            name: "Encrypt / decrypt text".to_string(),
            path: "/tools/encrypt-decrypt".to_string(),
            description: "Encrypt and decrypt text using various algorithms".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "encrypt".to_string(),
                "decrypt".to_string(),
                "cipher".to_string(),
            ],
        },
        Tool {
            name: "BIP39 passphrase generator".to_string(),
            path: "/tools/bip39-generator".to_string(),
            description: "Generate BIP39 passphrases for cryptocurrency wallets".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "bip39".to_string(),
                "passphrase".to_string(),
                "crypto".to_string(),
            ],
        },
        Tool {
            name: "Hmac generator".to_string(),
            path: "/tools/hmac-generator".to_string(),
            description: "Generate HMAC signatures".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "hmac".to_string(),
                "signature".to_string(),
                "hash".to_string(),
            ],
        },
        Tool {
            name: "RSA key pair generator".to_string(),
            path: "/tools/rsa-generator".to_string(),
            description: "Generate RSA key pairs".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "rsa".to_string(),
                "key".to_string(),
                "pair".to_string(),
                "crypto".to_string(),
            ],
        },
        Tool {
            name: "Password strength analyser".to_string(),
            path: "/tools/password-strength".to_string(),
            description: "Analyze password strength".to_string(),
            category: "Crypto".to_string(),
            is_new: false,
            is_favorite: false,
            keywords: vec![
                "password".to_string(),
                "strength".to_string(),
                "security".to_string(),
            ],
        },
        Tool {
            name: "Facebook Video Downloader".to_string(),
            path: "/tools/facebook-video-downloader".to_string(),
            description: "Download videos from Facebook by providing a video URL".to_string(),
            category: "Social Media".to_string(),
            is_new: true,
            is_favorite: false,
            keywords: vec![
                "facebook".to_string(),
                "video".to_string(),
                "download".to_string(),
                "social".to_string(),
                "media".to_string(),
            ],
        },
    ]
}

/// Groups tools by their category
///
/// This function organizes all tools into categories and returns a vector of ToolCategory objects
pub fn get_tools_by_category() -> Vec<ToolCategory> {
    let tools = get_tools();
    let mut categories: Vec<ToolCategory> = Vec::new();

    // Group tools by category
    let mut category_map: std::collections::HashMap<String, Vec<Tool>> =
        std::collections::HashMap::new();

    for tool in tools {
        category_map
            .entry(tool.category.clone())
            .or_default()
            .push(tool);
    }

    // Convert the map to a vector of ToolCategory
    for (name, tools) in category_map {
        categories.push(ToolCategory { name, tools });
    }

    categories
}
