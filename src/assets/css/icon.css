/* Icon component styles */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}

.icon img {
  width: 100%;
  height: 100%;
}

/* Ensure SVG icons use the current text color */
.icon img {
  color: currentColor;
  fill: currentColor;
  stroke: currentColor;
}

/* SVG icons only */

/* Fallback icon styles */
.icon-fallback {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  background-color: #e5e7eb;
  color: #4b5563;
  border-radius: 50%;
  width: 1em;
  height: 1em;
  line-height: 1;
  text-transform: uppercase;
  font-weight: bold;
}

.dark .icon-fallback {
  background-color: #374151;
  color: #e5e7eb;
}

/* SVG Icon styles */
.svg-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 0;
  color: currentColor;
}

.svg-icon {
  color: inherit;
  fill: none;
  stroke: currentColor;
  width: 100%;
  height: 100%;
}

/* Fix for SVG icons in buttons */
button .svg-icon-wrapper,
a .svg-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Tool card icon styles */
.tool-card .svg-icon-wrapper {
  color: #e9e9e9; /* Light gray for proper contrast in both light and dark modes */
}

.dark .tool-card .svg-icon-wrapper {
  color: #e9e9e9; /* Light gray for proper contrast in both light and dark modes */
}

/* Tool card icon specific class */
.tool-card-icon {
  color: #e9e9e9 !important; /* Light gray for proper contrast in both light and dark modes */
  stroke: #e9e9e9 !important;
}

/* Menu icon styles */
.menu-icon-item {
  position: relative;
}

.menu-icon-item .svg-icon-wrapper {
  color: #e9e9e9; /* Light gray for proper contrast in both light and dark modes */
}

.menu-icon-item .badge {
  position: absolute;
  background-color: #18a058; /* Primary color */
  border-radius: 10px;
  line-height: 1;
  top: 3px;
  left: -6px;
  height: 6px;
  width: 6px;
}

.dark .menu-icon-item .badge {
  background-color: #63e2b7; /* Dark mode primary color */
}

/* Sidebar icon styles */
.sidebar-icon {
  color: #e9e9e9 !important; /* Light gray for proper contrast in both light and dark modes */
  stroke: #e9e9e9 !important;
}

/* Navbar icon styles */
.navbar .svg-icon-wrapper {
  color: currentColor;
}

/* Sidebar content icon adjustments - apply to all icons in sidebar */
.sider-content .svg-icon-wrapper svg {
  width: 22px !important;
  height: 22px !important;
  position: relative;
  left: -8px;
}

/* Rotation classes for icons */
.rotate-0 {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.rotate-90 {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.rotate-270 {
  transform: rotate(270deg);
  transition: transform 0.3s ease;
}
