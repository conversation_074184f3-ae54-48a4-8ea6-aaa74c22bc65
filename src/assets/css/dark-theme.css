/* Dark theme specific styles */
:root {
    --background-color: #1c1c1c;
    --text-color: #e5e7eb;
    --card-background: #232323;
    --card-border: #282828;
    --card-hover-border: #18a058;
    --card-shadow: none;
    --card-hover-shadow: none;
    --card-title-color: white;
    --card-description-color: #d1d5db;
    --card-category-color: #9ca3af;
    --button-default-bg: #374151;
    --button-default-color: #e5e7eb;
    --button-default-hover-bg: #4b5563;
    --button-text-hover-bg: #374151;
    --tool-card-icon-color: #525252;
}

/* Dark theme specific overrides */
body {
    background-color: var(--background-color);
    color: var(--text-color);
}

.content-container {
    background-color: var(--background-color);
}

.tool-card {
    background-color: var(--card-background);
    border-color: var(--card-border);
    box-shadow: var(--card-shadow);
}

.tool-card:hover {
    border-color: var(--card-hover-border);
    box-shadow: var(--card-hover-shadow);
}

.tool-card-title {
    color: var(--card-title-color);
}

.tool-card-description {
    color: var(--card-description-color);
}

.tool-card-category {
    color: var(--card-category-color);
}

.tool-card-icon {
    color: var(--tool-card-icon-color);
}

.btn-default {
    background-color: var(--button-default-bg);
    color: var(--button-default-color);
}

.btn-default:hover {
    background-color: var(--button-default-hover-bg);
}

.btn-text:hover {
    background-color: var(--button-text-hover-bg);
}
