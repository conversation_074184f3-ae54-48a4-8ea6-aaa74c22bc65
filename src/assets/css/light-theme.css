/* Light theme specific styles */
:root {
    --background-color: #f3f4f6;
    --text-color: #1f2937;
    --card-background: white;
    --card-border: transparent;
    --card-hover-border: #18a058;
    --card-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    --card-hover-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    --card-title-color: #111827;
    --card-description-color: #6b7280;
    --card-category-color: #9ca3af;
    --button-default-bg: #e5e7eb;
    --button-default-color: #1f2937;
    --button-default-hover-bg: #d1d5db;
    --button-text-hover-bg: #e5e7eb;
    --tool-card-icon-color: #9ca3af;
}

/* Light theme specific overrides */
body {
    background-color: var(--background-color);
    color: var(--text-color);
}

.content-container {
    background-color: var(--background-color);
}

.tool-card {
    background-color: var(--card-background);
    border-color: var(--card-border);
    box-shadow: var(--card-shadow);
}

.tool-card:hover {
    border-color: var(--card-hover-border);
    box-shadow: var(--card-hover-shadow);
}

.tool-card-title {
    color: var(--card-title-color);
}

.tool-card-description {
    color: var(--card-description-color);
}

.tool-card-category {
    color: var(--card-category-color);
}

.tool-card-icon {
    color: var(--tool-card-icon-color);
}

.btn-default {
    background-color: var(--button-default-bg);
    color: var(--button-default-color);
}

.btn-default:hover {
    background-color: var(--button-default-hover-bg);
}

.btn-text:hover {
    background-color: var(--button-text-hover-bg);
}
