@import "tailwindcss";
@import "./tailwind.config.css";

/* Import custom component styles */
@import './assets/css/icon.css';

/* Custom variables */
:root {
  --color-primary: #18a058;
  --color-primary-hover: #36ad6a;
  --color-primary-pressed: #0c7a43;
  --color-primary-light: #36ad6a22;

  --color-error: #d03050;
  --color-error-hover: #de576d;
  --color-error-pressed: #ab1f3f;
  --color-error-light: #d0305022;

  --color-warning: #f0a020;
  --color-warning-hover: #fcb040;
  --color-warning-pressed: #c97c10;

  --color-text-base: #333639;
  --color-text-muted: #8a9099;

  --color-bg-card: #ffffff;
  --color-border-card: #efeff5;

  --color-bg-input: #ffffff;
  --color-border-input: #d9d9d9;
}

.dark {
  color-scheme: dark;

  --color-text-base: #e5eaf3;
  --color-text-muted: #8a9099;

  --color-bg-card: #232323;
  --color-border-card: #282828;

  --color-bg-input: #333333;
  --color-border-input: #4d4d4d;
}

.light {
  color-scheme: light;
}

/* Base styles */
body {
  min-height: 100%;
  margin: 0;
  padding: 0;
  background-color: var(--color-background-light);
  color: var(--color-text-base-light);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

.dark body {
  background-color: var(--color-background-dark);
  color: var(--color-text-base-dark);
}

html {
  height: 100%;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

/* Override Tailwind preflight CSS for SVG elements */
svg {
  display: inline;
  vertical-align: initial;
}

@layer components {
  /* Layout styles */
  .menu-layout-container {
    @apply flex min-h-screen;
  }

  .menu-layout-container.menu-collapsed .sider-container {
    @apply w-0 overflow-hidden;
  }

  .menu-layout-container.menu-collapsed .hero-gradient-background {
    @apply hidden;
  }

  .sider-container {
    width: 240px;
    background-color: var(--color-surface-light);
    color: var(--color-text-base-light);
    border-right: 1px solid var(--color-border-light);
    transition: all 0.3s ease-in-out;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 20;
    overflow: hidden;
  }

  .dark .sider-container {
    background-color: var(--color-surface-dark);
    color: var(--color-text-base-dark);
    border-right-color: var(--color-border-dark);
  }

  /* Sidebar content container */
  .sider-content {
    position: relative;
    top: 160px; /* Position below the hero section */
    height: calc(100% - 160px); /* Take remaining height */
    overflow-y: auto;
    z-index: 5; /* Lower than hero section */
  }

  .content-container {
    flex: 1;
    margin-left: 240px;
    transition: all 0.3s ease-in-out;
    width: calc(100% - 240px);
    background-color: var(--color-background-light);
    overflow: auto;
    display: flex;
    flex-direction: column;
  }

  .dark .content-container {
    background-color: var(--color-background-dark);
  }

  .menu-layout-container.menu-collapsed .content-container {
    margin-left: 0;
    width: 100%;
  }

  .main-content {
    flex: 1;
    padding: 0 26px 26px 26px;
  }

  /* Hero section */
  .hero-wrapper {
    @apply block absolute left-0 w-full z-10 overflow-hidden no-underline;
    height: 160px;
    position: relative;
  }

  .hero-gradient {
    @apply absolute top-0 left-0 w-full overflow-hidden;
    height: 160px;
  }

  .hero-svg {
    @apply w-full;
    margin-top: -65px;
  }

  .dark .hero-svg {
    opacity: 0.8;
  }

  .text-wrapper {
    @apply absolute left-0 w-full text-center top-4 text-white;
  }

  .title {
    @apply text-[25px] font-semibold;
  }

  .divider {
    @apply w-[50px] h-0.5 rounded bg-primary mx-auto mb-[5px];
  }

  .subtitle {
    @apply text-base;
  }


  /* Scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thumb-gray-200::-webkit-scrollbar-thumb {
    background-color: #e5e7eb;
    border-radius: 4px;
  }

  .dark .scrollbar-thumb-gray-700::-webkit-scrollbar-thumb {
    background-color: #374151;
  }

  /* Firefox scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb transparent;
  }

  .dark .scrollbar-thin {
    scrollbar-color: #374151 transparent;
  }

  /* Overlay for small screens */
  .menu-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 15;
    cursor: pointer;
    display: none;
  }

  .is-small-screen:not(.menu-collapsed) .menu-overlay {
    display: block;
  }

  /* Mobile styles */
  @media (max-width: 768px) {
    .sm\:block .sider-container {
      @apply transform -translate-x-full transition-transform duration-300 ease-in-out w-[85%] max-w-[300px];
    }

    .sm\:block .sider-container:not(.w-0) {
      @apply translate-x-0;
    }

    .sm\:block .content-container {
      @apply ml-0 w-full;
    }

    .mobile-sidebar-icons {
      @apply flex flex-wrap justify-center gap-2;
    }

    .mobile-sidebar-icons button,
    .mobile-sidebar-icons a {
      @apply p-2;
    }
  }

  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 cursor-pointer;
    line-height: 1;
    height: 34px; /* medium size */
  }

  .btn-small {
    @apply text-xs;
    height: 28px;
  }

  .btn-large {
    @apply text-base;
    height: 40px;
  }

  /* Primary button */
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-hover active:bg-primary-pressed;
  }

  /* Default button */
  .btn-default {
    @apply bg-gray-100 text-text-base-light hover:bg-gray-200 active:bg-gray-300 dark:bg-gray-700 dark:text-text-base-dark dark:hover:bg-gray-600 dark:active:bg-gray-500;
  }

  /* Text button */
  .btn-text {
    @apply bg-transparent text-text-base-light hover:bg-gray-100 active:bg-gray-200 dark:text-text-base-dark dark:hover:bg-gray-700 dark:active:bg-gray-600;
  }

  /* Warning button */
  .btn-warning {
    @apply bg-warning-faded text-warning hover:bg-warning/20 active:bg-warning/30;
  }

  /* Error button */
  .btn-error {
    @apply bg-error-faded text-error hover:bg-error/20 active:bg-error/30;
  }

  /* Button shapes */
  .btn-circle {
    @apply rounded-full p-0 flex items-center justify-center;
    width: 34px; /* medium size */
  }

  .btn-circle.btn-small {
    width: 28px;
  }

  .btn-circle.btn-large {
    width: 40px;
  }

  .btn-round {
    @apply rounded-full;
  }

  /* Disabled state */
  .btn-disabled, .btn[disabled] {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Button types for text styling */
  .btn-type-primary {
    @apply text-primary;
  }

  .btn-type-success {
    @apply text-success;
  }

  .btn-type-warning {
    @apply text-warning;
  }

  .btn-type-error {
    @apply text-error;
  }

  .btn-type-default {
    @apply text-text-base-light dark:text-text-base-dark;
  }

  /* Link styles */
  .link {
    @apply text-primary hover:text-primary-hover active:text-primary-pressed underline focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  /* Alert styles */
  .alert {
    @apply p-4 rounded mb-4 relative border-l-4;
  }

  .alert-default {
    @apply bg-gray-100 text-gray-800 border-gray-400 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600;
  }

  .alert-success {
    @apply bg-success-faded text-success-pressed border-success dark:bg-success-faded/20 dark:text-success dark:border-success;
  }

  .alert-warning {
    @apply bg-warning-faded text-warning-pressed border-warning dark:bg-warning-faded/20 dark:text-warning dark:border-warning;
  }

  .alert-error {
    @apply bg-error-faded text-error-pressed border-error dark:bg-error-faded/20 dark:text-error dark:border-error;
  }

  .alert-title {
    @apply font-bold mb-2 text-base;
  }

  .alert-content {
    @apply text-sm;
  }

  .alert-close {
    @apply absolute top-2 right-2 text-lg font-bold cursor-pointer;
  }

  /* Card styles */
  .card {
    @apply bg-surface-light dark:bg-surface-dark border border-border-light dark:border-border-dark rounded-lg shadow-card dark:shadow-card-dark transition-shadow duration-200;
  }

  .card:hover {
    @apply shadow-card-hover dark:shadow-card-dark-hover;
  }

  /* Tool card styles */
  .tool-card {
    @apply bg-surface-light dark:bg-surface-dark rounded-lg shadow-card dark:shadow-card-dark overflow-hidden hover:shadow-card-hover dark:hover:shadow-card-dark-hover transition-shadow duration-200 border border-border-light dark:border-border-dark;
  }

  .tool-card-content {
    @apply p-4;
  }

  .tool-card-title {
    @apply text-lg font-bold mb-2 text-text-base-light dark:text-text-base-dark;
  }

  .tool-card-description {
    @apply text-sm text-text-muted-light dark:text-text-muted-dark mb-4;
  }

  .tool-card-category {
    @apply text-xs text-text-muted-light dark:text-text-muted-dark;
  }

  /* Favorite button */
  .favorite-button {
    @apply text-gray-400 hover:text-yellow-500 transition-colors duration-200 cursor-pointer;
  }

  .favorite-button.is-favorite {
    @apply text-yellow-500;
  }

  /* Tool layout */
  .tool-layout {
    @apply p-4 max-w-4xl mx-auto;
  }

  .tool-header {
    @apply mb-6;
  }

  .tool-title {
    @apply text-2xl font-bold mb-2 text-text-base-light dark:text-text-base-dark flex items-center gap-2;
  }

  .tool-description {
    @apply text-text-muted-light dark:text-text-muted-dark mb-6 text-base;
  }

  .tool-content {
    @apply bg-surface-light dark:bg-surface-dark rounded-lg shadow-card dark:shadow-card-dark p-6 border border-border-light dark:border-border-dark;
  }

  /* Input styles */
  .input-text {
    @apply w-full p-3 border rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
    @apply bg-input-bg-light dark:bg-input-bg-dark border-input-border-light dark:border-input-border-dark;
    @apply text-text-base-light dark:text-text-base-dark;
  }

  .input-text[readonly] {
    @apply bg-gray-100 dark:bg-gray-700 cursor-default;
  }

  .input-text[disabled] {
    @apply opacity-50 cursor-not-allowed;
  }

  .input-label {
    @apply block text-sm font-medium text-text-base-light dark:text-text-base-dark mb-2;
  }

  /* Page styles */
  .about-page, .not-found-page {
    @apply p-6 max-w-4xl mx-auto;
  }

  /* Command palette styles */
  .palette-modal .c-modal-container {
    @apply p-[12px] shadow-xl;
  }

  /* Ensure command palette appears above all other elements */
  .palette-modal {
    z-index: 9999 !important;
  }

  /* Command palette specific responsive styles */
  @media (max-width: 640px) {
    .palette-modal .c-modal-container {
      @apply max-w-[95vw] mt-[5vh] max-h-[90vh];
    }
  }

  /* Ensure placeholder text is visible in command palette */
  .palette-modal .input-text input::placeholder {
    color: #9ca3af;
    opacity: 1;
  }

  .dark .palette-modal .input-text input::placeholder {
    color: #6b7280;
    opacity: 1;
  }

  /* Prevent horizontal scrolling in command palette */
  .palette-modal .c-modal-container {
    overflow-x: hidden;
    max-width: 100%;
    box-sizing: border-box;
  }

  .palette-modal .c-modal-container * {
    max-width: 100%;
    box-sizing: border-box;
  }

  .palette-modal .input-text {
    @apply text-[18px];
  }

  .palette-modal .input-text input {
    @apply py-[4px] pl-[18px];
  }

  /* Component-specific search bar styles */
  .search-bar-button,
  .search-bar-input,
  .palette-modal .input-text {
    @apply border border-gray-200 dark:border-gray-600 rounded-md;
    height: 34px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  /* Dark mode search bar background */
  .dark .search-bar-button,
  .dark .search-bar-input input,
  .dark .palette-modal .input-text input {
    background-color: #2e2e2e;
  }

  /* Ensure search bar has consistent dimensions */
  .navbar-search-container .search-bar-button {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 2%;
    height: 34px;
  }

  /* Component-specific locale selector styles */
  .locale-selector {
    height: 34px;
    box-sizing: border-box;
  }

  /* Override default c-button padding */
  .search-bar-button {
    padding: 0 2% !important;
  }

  .palette-modal .c-modal--overlay {
    @apply items-start pt-[80px];
  }

  /* Command palette button styles */
  .c-button {
    line-height: 1;
    font-family: inherit;
    font-size: var(--9a468bfe);
    border: none;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
    height: var(--09441655);
    font-weight: 400;
    color: var(--e1cab870);
    padding: 0 2%;
    border-radius: 4px;
    transition: background-color cubic-bezier(0.4, 0, 0.2, 1) 0.3s;
    background-color: transparent;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: auto;
    box-sizing: border-box;
  }

  .c-button:not(.disabled):hover {
    background-color: var(--645976e7);
  }

  .c-button:not(.disabled):active {
    background-color: var(--cea95e96);
  }

  .c-button:focus {
    outline: 1px solid var(--c8f65826);
  }

  .important\:justify-start {
    justify-content: flex-start !important;
  }

  /* Ensure the command palette takes the right amount of space */
  .flex-1 {
    flex: 1 1 0%;
  }

  /* Navbar component styles */
  .navbar-button,
  .theme-toggle-button {
    height: 34px;
    width: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
  }

  /* Support button styles */
  .support-button {
    box-sizing: border-box;
    font-size: 12px;
    line-height: 1;
    background-color: #f9f9f9;
    color: #333;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    height: 34px;
  }

  .dark .support-button {
    background-color: #333;
    color: #f9f9f9;
  }

  .support-button:hover {
    background-color: #f0f0f0;
  }

  .dark .support-button:hover {
    background-color: #444;
  }

  /* Tool menu styles */
  .category-container {
    margin-bottom: 0.5rem;
  }

  .category-header {
    display: flex;
    align-items: center;
    cursor: pointer;
    opacity: 0.6;
    margin-left: 6px;
    margin-top: 12px;
  }

  .chevron {
    font-size: 1rem;
    line-height: 1;
    opacity: 0.5;
    transition: transform 0.2s;
  }

  .rotate-0 {
    transform: rotate(0deg);
  }

  .rotate-90 {
    transform: rotate(90deg);
  }

  .category-name {
    font-size: 0.875rem;
    margin-left: 8px;
  }

  .menu-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;
  }

  .hidden {
    display: none;
  }

  .toggle-bar {
    width: 1.5rem;
    opacity: 0.1;
    transition: opacity 0.2s;
    position: relative;
    cursor: pointer;
  }

  .toggle-bar:hover {
    opacity: 0.5;
  }

  .toggle-bar::before {
    width: 0.125rem;
    height: 100%;
    content: '';
    background-color: var(--text-muted-light);
    border-radius: 0.25rem;
    position: absolute;
    top: 0;
    left: 14px;
  }

  .dark .toggle-bar::before {
    background-color: var(--text-muted-dark);
  }

  .tool-list {
    display: flex;
    flex-direction: column;
    margin-bottom: 5px;
    width: 100%;
  }

  .tool-link {
    text-decoration: none;
    color: var(--text-base-light);
  }

  .dark .tool-link {
    color: var(--text-base-dark);
  }

  .tool-item {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-radius: 0.25rem;
    width: 100%; /* Use full width of container */
    max-width: 200px; /* Maximum width to ensure consistent layout */
    box-sizing: border-box;
    white-space: nowrap;
  }

  .tool-item:hover {
    background-color: #f3f4f6;
  }

  .dark .tool-item:hover {
    background-color: #1f2937;
  }

  .tool-item svg {
    color: var(--text-muted-light);
    margin-right: 0.5rem;
  }

  .dark .tool-item svg {
    color: var(--text-muted-dark);
  }

  .tool-name {
    font-size: 0.875rem;
    width: 100%;
    display: inline-block;
  }

  .tool-badge {
    display: inline-block;
    background-color: var(--primary);
    border-radius: 9999px;
    height: 6px;
    width: 6px;
    margin-left: -6px;
    margin-right: 6px;
    position: relative;
    top: -3px;
  }
}

/* Import override styles - must be after all other styles */
@import './assets/css/override.css';
