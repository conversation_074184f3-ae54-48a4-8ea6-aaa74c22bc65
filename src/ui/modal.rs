use crate::themes::use_theme;
use wasm_bindgen::{JsCast, closure::Closure};
use web_sys::Element;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ModalProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub open: bool,
    #[prop_or_default]
    pub centered: bool,
    #[prop_or(Callback::noop())]
    pub onclose: Callback<()>,
}

/// Modal component for displaying content in a modal dialog.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::Modal;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     let open = use_state(|| false);
///
///     let toggle = {
///         let open = open.clone();
///         Callback::from(move |_| {
///             open.set(!*open);
///         })
///     };
///
///     let onclose = {
///         let open = open.clone();
///         Callback::from(move |_| {
///             open.set(false);
///         })
///     };
///
///     html! {
///         <>
///             <button onclick={toggle}>{"Open Modal"}</button>
///             <Modal open={*open} onclose={onclose}>
///                 <h2>{"Modal Title"}</h2>
///                 <p>{"Modal content goes here"}</p>
///             </Modal>
///         </>
///     }
/// }
/// ```
#[function_component(Modal)]
pub fn modal(props: &ModalProps) -> Html {
    let _theme = use_theme();
    let modal_ref = use_node_ref();

    // Handle click outside to close modal
    let onclick_outside = {
        let modal_ref = modal_ref.clone();
        let onclose = props.onclose.clone();

        Callback::from(move |event: MouseEvent| {
            if let Some(modal_element) = modal_ref.cast::<Element>() {
                let target = event.target_dyn_into::<Element>();

                if let Some(target_element) = target {
                    // Check if click is outside the modal
                    if !modal_element.contains(Some(&target_element)) {
                        onclose.emit(());
                    }
                }
            }
        })
    };

    // Add transition effect
    let transition_state = use_state(|| props.open);
    let animation_in_progress = use_state(|| false);

    {
        let transition_state = transition_state.clone();
        let animation_in_progress = animation_in_progress.clone();
        let open = props.open;

        use_effect_with(open, move |open| {
            let cleanup = if *open {
                transition_state.set(true);
                animation_in_progress.set(true);

                let animation_in_progress = animation_in_progress.clone();
                web_sys::window()
                    .unwrap()
                    .set_timeout_with_callback_and_timeout_and_arguments_0(
                        Closure::once_into_js(move || {
                            animation_in_progress.set(false);
                        })
                        .unchecked_ref(),
                        300, // Duration in ms
                    )
                    .unwrap()
            } else {
                animation_in_progress.set(true);

                let transition_state = transition_state.clone();
                let animation_in_progress = animation_in_progress.clone();
                web_sys::window()
                    .unwrap()
                    .set_timeout_with_callback_and_timeout_and_arguments_0(
                        Closure::once_into_js(move || {
                            transition_state.set(false);
                            animation_in_progress.set(false);
                        })
                        .unchecked_ref(),
                        300, // Duration in ms
                    )
                    .unwrap()
            };

            move || {
                web_sys::window()
                    .unwrap()
                    .clear_timeout_with_handle(cleanup);
            }
        });
    }

    if !*transition_state {
        return html! {};
    }

    let overlay_classes = classes!(
        "fixed",
        "left-0",
        "top-0",
        "z-50",
        "h-full",
        "w-full",
        "flex",
        "justify-center",
        "px-2",
        "bg-black",
        "bg-opacity-50",
        if props.centered {
            "items-center"
        } else {
            "items-start"
        },
        if *animation_in_progress {
            "opacity-0"
        } else {
            "opacity-100"
        },
        "transition-opacity",
        "duration-300",
        "ease-in-out",
    );

    let container_classes = classes!(
        "c-modal-container",
        "max-w-xl",
        "w-full",
        if props.centered {
            "flex-grow"
        } else {
            "flex-shrink-0"
        },
        "rounded-md",
        "p-6",
        "bg-surface-light",
        "dark:bg-surface-dark",
        "shadow-lg",
        if *animation_in_progress {
            "scale-95"
        } else {
            "scale-100"
        },
        "transition-transform",
        "duration-300",
        "ease-in-out",
        props.class.clone(),
    );

    html! {
        <div class={overlay_classes} onclick={onclick_outside.clone()}>
            <div ref={modal_ref} class={container_classes} onclick={Callback::from(|e: MouseEvent| e.stop_propagation())}>
                { for props.children.iter() }
            </div>
        </div>
    }
}
