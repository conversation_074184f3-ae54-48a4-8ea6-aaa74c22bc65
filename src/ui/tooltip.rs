use crate::themes::use_theme;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct TooltipProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub tooltip: Option<String>,
    #[prop_or(String::from("top"))]
    pub position: String,
    #[prop_or_default]
    pub tooltip_class: Classes,
    #[prop_or_default]
    pub onclick: Callback<MouseEvent>,
}

/// Tooltip component for displaying a tooltip when hovering over an element.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::Tooltip;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <Tooltip tooltip="This is a tooltip" position="top">
///             <button>{"Hover me"}</button>
///         </Tooltip>
///     }
/// }
/// ```
#[function_component(Tooltip)]
pub fn tooltip(props: &TooltipProps) -> Html {
    let _theme = use_theme();
    let is_hovered = use_state(|| false);
    let target_ref = use_node_ref();

    // Handle hover events
    let onmouseenter = {
        let is_hovered = is_hovered.clone();
        Callback::from(move |_| {
            is_hovered.set(true);
        })
    };

    let onmouseleave = {
        let is_hovered = is_hovered.clone();
        Callback::from(move |_| {
            is_hovered.set(false);
        })
    };

    // Determine tooltip position classes
    let tooltip_position_classes = match props.position.as_str() {
        "bottom" => classes!("top-100%", "left-50%", "-translate-x-1/2", "mt-[5px]"),
        "left" => classes!("right-100%", "top-50%", "-translate-y-1/2", "mr-[5px]"),
        "right" => classes!("left-100%", "top-50%", "-translate-y-1/2", "ml-[5px]"),
        _ => classes!("bottom-100%", "left-50%", "-translate-x-1/2", "mb-[5px]"), // default is "top"
    };

    // Base tooltip classes
    let tooltip_classes = classes!(
        "absolute",
        "z-10",
        "whitespace-nowrap",
        "rounded",
        "bg-black",
        "px-[12px]",
        "py-[6px]",
        "text-sm",
        "text-white",
        "shadow-lg",
        "transition",
        "duration-200",
        if *is_hovered {
            "opacity-100 scale-100"
        } else {
            "opacity-0 scale-0"
        },
        tooltip_position_classes,
        props.tooltip_class.clone(),
    );

    html! {
        <div
            class={classes!("relative", "inline-block", props.class.clone())}
            onclick={props.onclick.clone()}
            onmouseenter={onmouseenter}
            onmouseleave={onmouseleave}
        >
            <div ref={target_ref}>
                { for props.children.iter() }
            </div>

            if let Some(tooltip_text) = &props.tooltip {
                if *is_hovered {
                    <div class={tooltip_classes}>
                        { tooltip_text }
                    </div>
                }
            }
        </div>
    }
}
