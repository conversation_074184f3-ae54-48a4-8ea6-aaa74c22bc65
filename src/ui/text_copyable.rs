use crate::themes::use_theme;
use crate::ui::Tooltip;
use gloo::timers::callback::Timeout;
use wasm_bindgen::JsCast;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct TextCopyableProps {
    #[prop_or_default]
    pub value: String,
    #[prop_or_default]
    pub displayed_value: Option<String>,
    #[prop_or(true)]
    pub show_icon: bool,
    #[prop_or_default]
    pub class: Classes,
}

/// TextCopyable component for displaying text that can be copied to clipboard.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::TextCopyable;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <TextCopyable value="Text to copy" />
///     }
/// }
/// ```
#[function_component(TextCopyable)]
pub fn text_copyable(props: &TextCopyableProps) -> Html {
    let _theme = use_theme();
    let is_just_copied = use_state(|| false);

    // Copy function
    let copy = {
        let value = props.value.clone();
        let is_just_copied = is_just_copied.clone();

        Callback::from(move |_| {
            // Use the document.execCommand API as a fallback for clipboard API
            let document = web_sys::window().unwrap().document().unwrap();

            // Create a temporary textarea element
            let textarea = document.create_element("textarea").unwrap();
            let textarea = textarea.unchecked_into::<web_sys::HtmlTextAreaElement>();

            // Set the value and add it to the document
            textarea.set_value(&value);
            let _ = document.body().unwrap().append_child(&textarea);

            // Select the text
            textarea.select();

            // Execute the copy command using document.execCommand (with proper JS interop)
            let _ = js_sys::Reflect::get(&document, &"execCommand".into())
                .ok()
                .and_then(|exec_command| {
                    if exec_command.is_function() {
                        js_sys::Function::from(exec_command)
                            .call1(&document, &"copy".into())
                            .ok()
                    } else {
                        None
                    }
                });

            // Remove the textarea
            textarea.remove();

            // Set copied state
            is_just_copied.set(true);

            // Reset copied state after 2 seconds
            let is_just_copied_clone = is_just_copied.clone();
            let timeout = Timeout::new(2000, move || {
                is_just_copied_clone.set(false);
            });
            timeout.forget();
        })
    };

    // Determine what text to display
    let display_text = props
        .displayed_value
        .clone()
        .unwrap_or_else(|| props.value.clone());

    // Tooltip text based on copy state
    let tooltip_text = if *is_just_copied {
        "Copied!".to_string()
    } else {
        "Copy to clipboard".to_string()
    };

    html! {
        <Tooltip tooltip={tooltip_text} class={classes!("cursor-pointer", props.class.clone())} onclick={copy}>
            <span class="flex items-center gap-2">
                { display_text }
                if props.show_icon {
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 opacity-40"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                        />
                    </svg>
                }
            </span>
        </Tooltip>
    }
}
