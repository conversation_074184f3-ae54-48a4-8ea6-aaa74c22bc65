use crate::router::Route;
use crate::stores::use_style_store;
use crate::themes::use_theme;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ButtonProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub onclick: Callback<MouseEvent>,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub disabled: bool,
    #[prop_or(String::from("basic"))]
    pub variant: String,
    #[prop_or(String::from("default"))]
    pub r#type: String,
    #[prop_or_default]
    pub round: bool,
    #[prop_or_default]
    pub circle: bool,
    #[prop_or(String::from("medium"))]
    pub size: String,
    #[prop_or_default]
    pub href: Option<String>,
    #[prop_or_default]
    pub to: Option<Route>,
    #[prop_or_default]
    pub aria_label: Option<String>,
}

/// Button component that supports different variants, types, sizes, and can be used as a link or router link.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::Button;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <Button variant="basic" r#type="primary" size="medium">
///             {"Click me"}
///         </Button>
///     }
/// }
/// ```
#[function_component(Button)]
pub fn button(props: &ButtonProps) -> Html {
    let _theme = use_theme();
    let style_store = use_style_store();
    let is_dark_theme = style_store.is_dark_theme;

    // Determine variant class
    let variant_class = match props.variant.as_str() {
        "text" => "btn-text",
        _ => match props.r#type.as_str() {
            "primary" => "btn-primary",
            "warning" => "btn-warning",
            "error" => "btn-error",
            _ => "btn-default",
        },
    };

    // Determine type class for text variant
    let type_class = if props.variant == "text" {
        match props.r#type.as_str() {
            "primary" => "btn-type-primary",
            "success" => "btn-type-success",
            "warning" => "btn-type-warning",
            "error" => "btn-type-error",
            _ => {
                if is_dark_theme {
                    "btn-type-default dark:text-white"
                } else {
                    "btn-type-default"
                }
            }
        }
    } else {
        ""
    };

    // Determine size class
    let size_class = match props.size.as_str() {
        "small" => "btn-small",
        "large" => "btn-large",
        _ => "", // medium is default
    };

    // Determine shape classes
    let shape_classes = classes!(
        props.round.then_some("btn-round"),
        props.circle.then_some("btn-circle"),
    );

    // Handle click event
    let onclick = {
        let onclick = props.onclick.clone();
        let disabled = props.disabled;

        Callback::from(move |event: MouseEvent| {
            if !disabled {
                onclick.emit(event);
            }
        })
    };

    // Render as link if href is provided
    if let Some(href) = &props.href {
        return html! {
            <a
                href={href.clone()}
                class={classes!(
                    "btn",
                    variant_class,
                    type_class,
                    size_class,
                    shape_classes,
                    props.disabled.then_some("btn-disabled"),
                    props.class.clone()
                )}
                onclick={onclick}
                aria_label={props.aria_label.clone()}
            >
                { for props.children.iter() }
            </a>
        };
    }

    // Render as router link if to is provided
    if let Some(to) = &props.to {
        return html! {
            <yew_router::components::Link<Route>
                to={to.clone()}
                classes={classes!(
                    "btn",
                    variant_class,
                    type_class,
                    size_class,
                    shape_classes,
                    props.disabled.then_some("btn-disabled"),
                    props.class.clone()
                )}
            >
                { for props.children.iter() }
            </yew_router::components::Link<Route>>
        };
    }

    // Render as button
    html! {
        <button
            class={classes!(
                "btn",
                variant_class,
                type_class,
                size_class,
                shape_classes,
                props.class.clone()
            )}
            onclick={onclick}
            disabled={props.disabled}
            aria_label={props.aria_label.clone()}
        >
            { for props.children.iter() }
        </button>
    }
}
