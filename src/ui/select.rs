use crate::themes::use_theme;
use gloo::events::EventListener;
use wasm_bindgen::JsCast;
use web_sys::{Element, HtmlElement, KeyboardEvent, MouseEvent as DomMouseEvent};
use yew::prelude::*;

#[derive(Clone, PartialEq)]
pub struct SelectOption {
    pub label: String,
    pub value: String,
    pub tooltip: Option<String>,
}

// Implementation removed as these methods were never used
impl SelectOption {}

#[derive(Properties, PartialEq)]
pub struct SelectProps {
    #[prop_or_default]
    pub options: Vec<SelectOption>,
    #[prop_or_default]
    pub value: Option<String>,
    #[prop_or_default]
    pub placeholder: Option<String>,
    #[prop_or(String::from("medium"))]
    pub size: String,
    #[prop_or(false)]
    pub searchable: bool,
    #[prop_or_default]
    pub label: Option<String>,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or(Callback::noop())]
    pub onchange: Callback<String>,
}

/// Select component for selecting an option from a dropdown.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::{Select, SelectOption};
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     let options = vec![
///         SelectOption::new("Option 1", "1"),
///         SelectOption::new("Option 2", "2"),
///         SelectOption::new("Option 3", "3"),
///     ];
///
///     let value = use_state(|| None);
///
///     let onchange = {
///         let value = value.clone();
///         Callback::from(move |new_value: String| {
///             value.set(Some(new_value));
///         })
///     };
///
///     html! {
///         <Select
///             options={options}
///             value={(*value).clone()}
///             placeholder="Select an option"
///             onchange={onchange}
///         />
///     }
/// }
/// ```
#[function_component(Select)]
pub fn select(props: &SelectProps) -> Html {
    let _theme = use_theme();
    let is_open = use_state(|| false);
    let selected_option = use_state(|| {
        props
            .options
            .iter()
            .find(|option| Some(option.value.clone()) == props.value)
            .cloned()
    });
    let focus_index = use_state(|| 0);
    let element_ref = use_node_ref();
    let dropdown_ref = use_node_ref();
    let search_input_ref = use_node_ref();
    let search_query = use_state(String::new);

    // Close dropdown when clicking outside
    {
        let element_ref = element_ref.clone();
        let is_open = is_open.clone();

        use_effect_with(
            (element_ref.clone(), is_open.clone()),
            move |(element_ref, is_open)| {
                if !**is_open {
                    return Box::new(|| ()) as Box<dyn FnOnce()>;
                }

                let document = web_sys::window().unwrap().document().unwrap();

                let click_listener = {
                    let element_ref = element_ref.clone();
                    let is_open = is_open.clone();

                    EventListener::new(&document, "mousedown", move |event| {
                        let event = event.dyn_ref::<DomMouseEvent>().unwrap();
                        if let Some(element) = element_ref.cast::<Element>() {
                            let target = event.target().unwrap();
                            let target_element = target.dyn_ref::<Element>().unwrap();

                            if !element.contains(Some(target_element)) {
                                is_open.set(false);
                            }
                        }
                    })
                };

                // Store listener for cleanup
                let click_listener_clone = click_listener;

                // Return cleanup function
                Box::new(move || {
                    // EventListener automatically removes the event listener when dropped
                    drop(click_listener_clone);
                }) as Box<dyn FnOnce()>
            },
        );
    }

    // Focus search input when dropdown opens
    {
        let search_input_ref = search_input_ref.clone();
        let is_open = is_open.clone();
        let searchable = props.searchable;

        use_effect_with(
            (is_open.clone(), searchable),
            move |(is_open, searchable)| {
                if **is_open && *searchable {
                    if let Some(input) = search_input_ref.cast::<HtmlElement>() {
                        let _ = input.focus();
                    }
                }
                || {}
            },
        );
    }

    // Reset focus index and search query when dropdown closes
    {
        let is_open = is_open.clone();
        let focus_index = focus_index.clone();
        let search_query = search_query.clone();

        use_effect_with(is_open.clone(), move |is_open| {
            if !**is_open {
                focus_index.set(0);
                search_query.set(String::new());
            }
            || {}
        });
    }

    // Filter options based on search query
    let filtered_options = {
        let search_query = (*search_query).clone();
        if search_query.is_empty() {
            props.options.clone()
        } else {
            props
                .options
                .iter()
                .filter(|option| {
                    option
                        .label
                        .to_lowercase()
                        .contains(&search_query.to_lowercase())
                })
                .cloned()
                .collect::<Vec<_>>()
        }
    };

    // Toggle dropdown
    let toggle_open = {
        let is_open = is_open.clone();

        Callback::from(move |_| {
            is_open.set(!*is_open);
        })
    };

    // Select option
    let select_option = {
        let is_open = is_open.clone();
        let selected_option = selected_option.clone();
        let onchange = props.onchange.clone();

        Callback::from(move |option: SelectOption| {
            selected_option.set(Some(option.clone()));
            is_open.set(false);
            onchange.emit(option.value);
        })
    };

    // Handle keyboard navigation
    let handle_keydown = {
        let is_open = is_open.clone();
        let focus_index = focus_index.clone();
        let filtered_options = filtered_options.clone();
        let select_option = select_option.clone();

        Callback::from(move |event: KeyboardEvent| {
            if !*is_open {
                if event.key() == "Enter" || event.key() == " " || event.key() == "ArrowDown" {
                    event.prevent_default();
                    is_open.set(true);
                }
                return;
            }

            match event.key().as_str() {
                "Escape" => {
                    event.prevent_default();
                    is_open.set(false);
                }
                "ArrowDown" => {
                    event.prevent_default();
                    let new_index = (*focus_index + 1) % filtered_options.len();
                    focus_index.set(new_index);
                }
                "ArrowUp" => {
                    event.prevent_default();
                    let new_index = if *focus_index == 0 {
                        filtered_options.len() - 1
                    } else {
                        *focus_index - 1
                    };
                    focus_index.set(new_index);
                }
                "Enter" => {
                    event.prevent_default();
                    if !filtered_options.is_empty() {
                        let option = filtered_options[*focus_index].clone();
                        select_option.emit(option);
                    }
                }
                _ => {}
            }
        })
    };

    // Handle search input
    let on_search_input = {
        let search_query = search_query.clone();
        let focus_index = focus_index.clone();

        Callback::from(move |event: InputEvent| {
            let input: HtmlElement = event.target_unchecked_into();
            let value = input.inner_html();
            search_query.set(value);
            focus_index.set(0);
        })
    };

    // Determine size classes
    let size_class = match props.size.as_str() {
        "small" => "h-8 text-xs",
        "large" => "h-10 text-base",
        _ => "h-9 text-sm", // medium is default
    };

    html! {
        <div class={classes!("relative", "w-full", props.class.clone())}>
            if let Some(label) = &props.label {
                <label class="block text-sm font-medium text-text-base-light dark:text-text-base-dark mb-2">
                    { label }
                </label>
            }

            <div
                ref={element_ref}
                class={classes!(
                    "c-select",
                    "relative",
                    "w-full",
                    size_class
                )}
            >
                <div
                    class={classes!(
                        "flex",
                        "flex-nowrap",
                        "cursor-pointer",
                        "items-center",
                        "border",
                        "rounded-md",
                        "px-3",
                        "transition-colors",
                        "duration-200",
                        "bg-input-bg-light",
                        "dark:bg-input-bg-dark",
                        "border-input-border-light",
                        "dark:border-input-border-dark",
                        "hover:border-primary",
                        "focus:outline-none",
                        "focus:ring-2",
                        "focus:ring-primary",
                        "focus:border-primary",
                        if *is_open { "border-primary ring-2 ring-primary" } else { "" },
                        "h-full"
                    )}
                    tabindex="0"
                    onclick={toggle_open}
                    onkeydown={handle_keydown}
                >
                    <div class="flex-1 truncate">
                        if props.searchable && *is_open {
                            <input
                                ref={search_input_ref}
                                type="text"
                                placeholder="Search..."
                                class="w-full bg-transparent border-none focus:outline-none focus:ring-0 p-0"
                                value={(*search_query).clone()}
                                oninput={on_search_input}
                            />
                        } else if let Some(option) = (*selected_option).clone() {
                            <span class="leading-normal">
                                { option.label }
                            </span>
                        } else {
                            <span class="leading-normal text-text-muted-light dark:text-text-muted-dark">
                                { props.placeholder.clone().unwrap_or_else(|| "Select an option".to_string()) }
                            </span>
                        }
                    </div>

                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class={classes!(
                            "h-4",
                            "w-4",
                            "transition-transform",
                            "duration-200",
                            if *is_open { "rotate-180" } else { "" }
                        )}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </div>

                if *is_open {
                    <div
                        ref={dropdown_ref}
                        class="c-select-dropdown absolute z-10 mt-1 max-h-[312px] w-full overflow-y-auto rounded-md border border-input-border-light dark:border-input-border-dark bg-input-bg-light dark:bg-input-bg-dark shadow-lg"
                    >
                        if filtered_options.is_empty() {
                            <div class="px-4 py-2 text-text-muted-light dark:text-text-muted-dark">
                                {"No results found"}
                            </div>
                        } else {
                            {
                                filtered_options.iter().enumerate().map(|(index, option)| {
                                    let is_selected = selected_option.as_ref().is_some_and(|selected| selected.value == option.value);
                                    let is_focused = index == *focus_index;

                                    let _option_clone = option.clone();
                                    let onclick = {
                                        let select_option = select_option.clone();
                                        let option = option.clone();

                                        Callback::from(move |_| {
                                            select_option.emit(option.clone());
                                        })
                                    };

                                    html! {
                                        <div
                                            key={option.value.clone()}
                                            class={classes!(
                                                "cursor-pointer",
                                                "px-4",
                                                "py-2",
                                                "transition-colors",
                                                "duration-200",
                                                is_selected.then_some("text-primary"),
                                                is_focused.then_some("bg-gray-100 dark:bg-gray-700")
                                            )}
                                            onclick={onclick}
                                        >
                                            { &option.label }
                                        </div>
                                    }
                                }).collect::<Html>()
                            }
                        }
                    </div>
                }
            </div>
        </div>
    }
}
