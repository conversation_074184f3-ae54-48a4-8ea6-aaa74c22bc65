use crate::themes::use_theme;
use crate::ui::TextCopyable;
use yew::prelude::*;

#[derive(Clone, PartialEq)]
pub struct KeyValueItem {
    pub label: String,
    pub value: String,
    pub copyable: bool,
    pub hide_on_nil: bool,
}

// Implementation removed as these methods were never used
impl KeyValueItem {}

#[derive(Properties, PartialEq)]
pub struct KeyValueListProps {
    #[prop_or_default]
    pub items: Vec<KeyValueItem>,
    #[prop_or_default]
    pub class: Classes,
}

/// KeyValueList component for displaying a list of key-value pairs.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::{KeyValueList, KeyValueItem};
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     let items = vec![
///         KeyValueItem::new("Name", "John Doe"),
///         KeyValueItem::new("Email", "<EMAIL>"),
///         KeyValueItem::new("Phone", "****** 567 890"),
///     ];
///
///     html! {
///         <KeyValueList items={items} />
///     }
/// }
/// ```
#[function_component(KeyValueList)]
pub fn key_value_list(props: &KeyValueListProps) -> Html {
    let _theme = use_theme();

    // Filter out items with nil values if hide_on_nil is true
    let filtered_items = props
        .items
        .iter()
        .filter(|item| !item.value.is_empty() || !item.hide_on_nil)
        .collect::<Vec<_>>();

    html! {
        <div class={classes!("flex", "flex-col", "gap-2", props.class.clone())}>
            {
                filtered_items.iter().map(|item| {
                    html! {
                        <div class="key-value-list-item" key={item.label.clone()}>
                            <div class="key-value-list-key text-[13px] leading-normal text-text-muted-light dark:text-text-muted-dark">
                                { &item.label }
                            </div>

                            <div class="key-value-list-value font-bold leading-normal">
                                if item.copyable {
                                    <TextCopyable value={item.value.clone()} />
                                } else {
                                    { &item.value }
                                }
                            </div>
                        </div>
                    }
                }).collect::<Html>()
            }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct KeyValueListItemProps {
    #[prop_or_default]
    pub label: String,
    #[prop_or_default]
    pub value: String,
    #[prop_or(true)]
    pub copyable: bool,
    #[prop_or_default]
    pub class: Classes,
}

/// KeyValueListItem component for displaying a single key-value pair.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::KeyValueListItem;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <KeyValueListItem label="Name" value="John Doe" />
///     }
/// }
/// ```
#[function_component(KeyValueListItem)]
pub fn key_value_list_item(props: &KeyValueListItemProps) -> Html {
    let _theme = use_theme();

    html! {
        <div class={classes!("flex", "flex-col", props.class.clone())}>
            <div class="text-[13px] leading-normal text-text-muted-light dark:text-text-muted-dark">
                { &props.label }
            </div>

            <div class="font-bold leading-normal">
                if props.copyable {
                    <TextCopyable value={props.value.clone()} />
                } else {
                    { &props.value }
                }
            </div>
        </div>
    }
}
