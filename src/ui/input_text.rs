use crate::themes::use_theme;
use web_sys::{HtmlInputElement, InputEvent};
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct InputTextProps {
    #[prop_or_default]
    pub value: String,
    #[prop_or_default]
    pub placeholder: Option<String>,
    #[prop_or_default]
    pub label: Option<String>,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub input_class: Classes,
    #[prop_or_default]
    pub disabled: bool,
    #[prop_or_default]
    pub readonly: bool,
    #[prop_or(String::from("text"))]
    pub r#type: String,
    #[prop_or_default]
    pub autofocus: bool,
    #[prop_or_default]
    pub node_ref: NodeRef,
    #[prop_or(Callback::noop())]
    pub onchange: Callback<String>,
    #[prop_or(Callback::noop())]
    pub oninput: Callback<String>,
    #[prop_or(Callback::noop())]
    pub onfocus: Callback<FocusEvent>,
    #[prop_or(Callback::noop())]
    pub onblur: Callback<FocusEvent>,
    #[prop_or(Callback::noop())]
    pub onkeydown: Callback<KeyboardEvent>,
}

/// Input text component for text input.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::InputText;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     let value = use_state(|| String::from(""));
///
///     let onchange = {
///         let value = value.clone();
///         Callback::from(move |new_value: String| {
///             value.set(new_value);
///         })
///     };
///
///     html! {
///         <InputText
///             value={(*value).clone()}
///             placeholder="Enter text..."
///             label="Text Input"
///             onchange={onchange}
///         />
///     }
/// }
/// ```
#[function_component(InputText)]
pub fn input_text(props: &InputTextProps) -> Html {
    let _theme = use_theme();

    let handle_input = {
        let onchange = props.onchange.clone();
        let oninput = props.oninput.clone();

        Callback::from(move |e: InputEvent| {
            let input: HtmlInputElement = e.target_unchecked_into();
            let value = input.value();
            onchange.emit(value.clone());
            oninput.emit(value);
        })
    };

    let input_classes = classes!(
        "input-text",
        props.disabled.then_some("disabled"),
        props.readonly.then_some("readonly"),
        props.input_class.clone(),
    );

    html! {
        <div class={classes!("w-full", props.class.clone())}>
            if let Some(label) = &props.label {
                <label class="input-label">
                    { label }
                </label>
            }

            <input
                type={props.r#type.clone()}
                value={props.value.clone()}
                placeholder={props.placeholder.clone()}
                class={input_classes}
                disabled={props.disabled}
                readonly={props.readonly}
                autofocus={props.autofocus}
                ref={props.node_ref.clone()}
                oninput={handle_input}
                onfocus={props.onfocus.clone()}
                onblur={props.onblur.clone()}
                onkeydown={props.onkeydown.clone()}
            />
        </div>
    }
}
