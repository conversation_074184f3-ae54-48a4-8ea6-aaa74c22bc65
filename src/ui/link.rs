use crate::router::Route;
use crate::stores::use_style_store;
use crate::themes::use_theme;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct LinkProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub href: AttrValue,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub to: Option<Route>,
    #[prop_or_default]
    pub target: Option<String>,
    #[prop_or_default]
    pub rel: Option<String>,
    #[prop_or_default]
    pub onclick: Callback<MouseEvent>,
    #[prop_or_default]
    pub aria_label: Option<String>,
}

/// Link component that can be used as a router link or external link.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::Link;
/// use crate::router::Route;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <>
///             <Link to={Some(Route::Home)}>{"Go to Home"}</Link>
///             <Link href="https://example.com" target={Some("_blank".to_string())}>
///                 {"External Link"}
///             </Link>
///         </>
///     }
/// }
/// ```
#[function_component(Link)]
pub fn link(props: &LinkProps) -> Html {
    let _theme = use_theme();
    let style_store = use_style_store();
    let _is_dark_theme = style_store.is_dark_theme;

    // Base classes for all link types
    let base_classes = classes!(
        "link",
        "transition-colors",
        "duration-200",
        props.class.clone()
    );

    // If we have a route, render as a router link
    if let Some(to) = &props.to {
        return html! {
            <yew_router::components::Link<Route>
                to={to.clone()}
                classes={base_classes}
            >
                { for props.children.iter() }
            </yew_router::components::Link<Route>>
        };
    }

    // Otherwise render as a regular anchor
    html! {
        <a
            href={props.href.clone()}
            class={base_classes}
            target={props.target.clone()}
            rel={props.rel.clone().or_else(|| {
                // Add noopener and noreferrer for security if target is _blank
                if props.target == Some("_blank".to_string()) {
                    Some("noopener noreferrer".to_string())
                } else {
                    None
                }
            })}
            onclick={props.onclick.clone()}
            aria_label={props.aria_label.clone()}
        >
            { for props.children.iter() }
        </a>
    }
}
