use crate::themes::use_theme;
use crate::ui::{<PERSON><PERSON>, Tooltip};
use yew::prelude::*;

#[derive(Clone, PartialEq)]
pub struct ButtonsSelectOption {
    pub label: String,
    pub value: String,
    pub tooltip: Option<String>,
}

// Implementation removed as these methods were never used
impl ButtonsSelectOption {}

#[derive(Properties, PartialEq)]
pub struct ButtonsSelectProps {
    #[prop_or_default]
    pub options: Vec<ButtonsSelectOption>,
    #[prop_or_default]
    pub value: Option<String>,
    #[prop_or_default]
    pub label: Option<String>,
    #[prop_or(String::from("top"))]
    pub label_position: String,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or(Callback::noop())]
    pub onchange: Callback<String>,
}

/// ButtonsSelect component for selecting an option from a group of buttons.
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::{ButtonsSelect, ButtonsSelectOption};
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     let options = vec![
///         ButtonsSelectOption::new("Option 1", "1"),
///         ButtonsSelectOption::new("Option 2", "2"),
///         ButtonsSelectOption::new("Option 3", "3"),
///     ];
///
///     let value = use_state(|| None);
///
///     let onchange = {
///         let value = value.clone();
///         Callback::from(move |new_value: String| {
///             value.set(Some(new_value));
///         })
///     };
///
///     html! {
///         <ButtonsSelect
///             options={options}
///             value={(*value).clone()}
///             label="Select an option:"
///             onchange={onchange}
///         />
///     }
/// }
/// ```
#[function_component(ButtonsSelect)]
pub fn buttons_select(props: &ButtonsSelectProps) -> Html {
    let _theme = use_theme();

    // Handle option selection
    let select_option = {
        let onchange = props.onchange.clone();

        Callback::from(move |value: String| {
            onchange.emit(value);
        })
    };

    // Determine label position classes
    let container_classes = match props.label_position.as_str() {
        "left" => classes!("flex", "items-center", "gap-2"),
        _ => classes!("flex", "flex-col", "gap-1"), // default is "top"
    };

    html! {
        <div class={classes!(container_classes, props.class.clone())}>
            if let Some(label) = &props.label {
                <label class="block text-sm font-medium text-text-base-light dark:text-text-base-dark">
                    { label }
                </label>
            }

            <div class="flex flex-wrap gap-2">
                {
                    props.options.iter().map(|option| {
                        let is_selected = props.value.as_ref() == Some(&option.value);

                        let option_value = option.value.clone();
                        let onclick = {
                            let select_option = select_option.clone();
                            let value = option.value.clone();

                            Callback::from(move |_| {
                                select_option.emit(value.clone());
                            })
                        };

                        let button = html! {
                            <Button
                                r#type={if is_selected { "primary" } else { "default" }}
                                variant="basic"
                                size="small"
                                onclick={onclick}
                            >
                                { option.label.clone() }
                            </Button>
                        };

                        if let Some(tooltip) = &option.tooltip {
                            html! {
                                <Tooltip tooltip={tooltip.clone()} key={option_value}>
                                    { button }
                                </Tooltip>
                            }
                        } else {
                            html! {
                                <div key={option_value}>
                                    { button }
                                </div>
                            }
                        }
                    }).collect::<Html>()
                }
            </div>
        </div>
    }
}
