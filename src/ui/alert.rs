use crate::stores::use_style_store;
use crate::themes::use_theme;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct AlertProps {
    #[prop_or_default]
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub title: Option<String>,
    #[prop_or_default]
    pub r#type: String,
    #[prop_or_default]
    pub closable: bool,
    #[prop_or_default]
    pub icon: Option<Html>,
}

/// Alert component for displaying messages with different types (default, success, warning, error).
///
/// # Examples
///
/// ```
/// use yew::prelude::*;
/// use crate::ui::Alert;
///
/// #[function_component(Example)]
/// fn example() -> Html {
///     html! {
///         <Alert r#type="warning" title="Warning">
///             {"This is a warning message"}
///         </Alert>
///     }
/// }
/// ```
#[function_component(Alert)]
pub fn alert(props: &AlertProps) -> Html {
    let _theme = use_theme();
    let style_store = use_style_store();
    let _is_dark_theme = style_store.is_dark_theme;

    let visible = use_state(|| true);

    let type_class = match props.r#type.as_str() {
        "success" => "alert-success",
        "warning" => "alert-warning",
        "error" => "alert-error",
        _ => "alert-default",
    };

    let on_close = {
        let visible = visible.clone();
        Callback::from(move |_| {
            visible.set(false);
        })
    };

    if !*visible {
        return html! {};
    }

    // Default icons based on alert type
    let default_icon = match props.r#type.as_str() {
        "success" => html! {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
        },
        "warning" => html! {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
        },
        "error" => html! {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        },
        _ => html! {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
        },
    };

    html! {
        <div class={classes!("alert", type_class, props.class.clone())}>
            <div class="flex items-center">
                <div class="alert-icon mr-4 text-[40px] opacity-60">
                    {props.icon.clone().unwrap_or(default_icon)}
                </div>

                <div class="alert-content flex-1">
                    if let Some(title) = &props.title {
                        <div class="alert-title text-[15px] font-semibold mb-1">{title}</div>
                    }
                    <div class="text-sm">
                        { for props.children.iter() }
                    </div>
                </div>

                if props.closable {
                    <button class="alert-close absolute top-2 right-2 text-lg font-bold cursor-pointer" onclick={on_close}>{"×"}</button>
                }
            </div>
        </div>
    }
}
