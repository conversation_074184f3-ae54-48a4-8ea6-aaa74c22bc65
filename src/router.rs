use yew::prelude::*;
use yew_router::prelude::*;

use crate::layouts::{BaseLayout, ToolLayout};
use crate::pages::{AboutPage, HomePage, NotFoundPage};
use crate::tools::facebook_video_downloader::FacebookVideoDownloader;

#[derive(Clone, Routable, PartialEq)]
pub enum Route {
    #[at("/")]
    Home,
    #[at("/about")]
    About,
    #[at("/tools/facebook-video-downloader")]
    FacebookVideoDownloader,
    #[not_found]
    #[at("/*")]
    NotFound,
}

pub fn switch(routes: Route) -> Html {
    match routes {
        Route::Home => html! {
            <BaseLayout>
                <HomePage />
            </BaseLayout>
        },
        Route::About => html! {
            <BaseLayout>
                <AboutPage />
            </BaseLayout>
        },
        Route::FacebookVideoDownloader => html! {
            <BaseLayout>
                <ToolLayout
                    tool_name="Facebook Video Downloader"
                    tool_description="Download videos from Facebook by providing a video URL"
                >
                    <FacebookVideoDownloader />
                </ToolLayout>
            </BaseLayout>
        },
        Route::NotFound => html! {
            <BaseLayout>
                <NotFoundPage />
            </BaseLayout>
        },
    }
}

#[function_component(AppRouter)]
pub fn app_router() -> Html {
    html! {
        <BrowserRouter>
            <Switch<Route> render={switch} />
        </BrowserRouter>
    }
}
