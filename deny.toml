# cargo-deny configuration for security and license checking
# This file is used by the CI/CD pipeline to ensure dependencies meet security and licensing requirements

[advisories]
# The path where the advisory database is cloned/fetched into
db-path = "~/.cargo/advisory-db"
# The url(s) of the advisory databases to use
db-urls = ["https://github.com/rustsec/advisory-db"]
# The lint level for unmaintained crates
unmaintained = "all"
# The lint level for crates that have been yanked from their source registry
yanked = "warn"
# A list of advisory IDs to ignore. Note that ignored advisories will still
# output a note when they are encountered.
ignore = [
    "RUSTSEC-2024-0370", # proc-macro-error is unmaintained but used by Yew
]

[licenses]
# List of explicitly allowed licenses
allow = [
    "MIT",
    "Apache-2.0",
    "Apache-2.0 WITH LLVM-exception",
    "Unicode-3.0",
    "LicenseRef-Proprietary", # For our private project
]

[bans]
# Lint level for when multiple versions of the same crate are detected
multiple-versions = "warn"
# Allow multiple versions of gloo crates due to Yew 0.21 dependency constraints
skip = [
    { name = "gloo", version = "0.8.1" },
    { name = "gloo", version = "0.10.0" },
    { name = "gloo-console", version = "0.2.3" },
    { name = "gloo-dialogs", version = "0.1.1" },
    { name = "gloo-events", version = "0.1.2" },
    { name = "gloo-file", version = "0.2.3" },
    { name = "gloo-history", version = "0.1.5" },
    { name = "gloo-net", version = "0.3.1" },
    { name = "gloo-net", version = "0.4.0" },
    { name = "gloo-render", version = "0.1.1" },
    { name = "gloo-storage", version = "0.2.2" },
    { name = "gloo-timers", version = "0.2.6" },
    { name = "gloo-utils", version = "0.1.7" },
    { name = "gloo-worker", version = "0.2.1" },
    { name = "gloo-worker", version = "0.4.0" },
    { name = "serde-wasm-bindgen", version = "0.5.0" },
    { name = "syn", version = "1.0.109" },
]

[sources]
# List of URLs for allowed crate registries
allow-registry = ["https://github.com/rust-lang/crates.io-index"]
