# Contributing to Yew IT Tools

Thank you for your interest in contributing to Yew IT Tools! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Development Workflow](#development-workflow)
- [Code Quality Standards](#code-quality-standards)
- [Testing Requirements](#testing-requirements)
- [Performance Guidelines](#performance-guidelines)
- [Security Considerations](#security-considerations)
- [Pull Request Process](#pull-request-process)
- [Branch Protection Rules](#branch-protection-rules)

## Development Workflow

### Branch Strategy

We follow a structured branching strategy:

- **`main`**: Production branch, automatically deployed to GitHub Pages
- **`dev`**: Development branch for integration and testing
- **`feat/*`**: Feature branches created from `dev`
- **`fix/*`**: Bug fix branches created from `dev`
- **`hotfix/*`**: Emergency fixes created from `main`

### Getting Started

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/yew-it-tools.git
   cd yew-it-tools
   ```

2. **Install Dependencies**
   ```bash
   # Install Rust dependencies
   cargo check
   
   # Install Node.js dependencies
   pnpm install
   ```

3. **Set Up Development Environment**
   ```bash
   # Start development server
   pnpm dev
   
   # Or run components separately:
   pnpm build:css    # Build Tailwind CSS
   trunk serve       # Start Rust/WASM dev server
   ```

### Creating a Feature Branch

```bash
# Start from dev branch
git checkout dev
git pull origin dev

# Create and switch to feature branch
git checkout -b feat/your-feature-name

# Make your changes...

# Commit with conventional commit format
git commit -m "feat: add new feature description"

# Push to your fork
git push origin feat/your-feature-name
```

## Code Quality Standards

### Rust Code Standards

- **Formatting**: Use `cargo fmt` for consistent formatting
- **Linting**: Address all `cargo clippy` warnings
- **Documentation**: Document public APIs with rustdoc comments
- **Error Handling**: Use proper error handling with `Result` types
- **Performance**: Optimize for WASM bundle size

### Frontend Standards

- **CSS**: Use Tailwind CSS utility classes
- **Responsive Design**: Ensure mobile-first responsive design
- **Accessibility**: Follow WCAG 2.1 guidelines
- **Performance**: Minimize CSS bundle size

### Code Review Checklist

Before submitting a PR, ensure:

- [ ] Code follows Rust formatting standards (`cargo fmt`)
- [ ] No clippy warnings (`cargo clippy`)
- [ ] All tests pass (`cargo test`)
- [ ] Documentation is updated
- [ ] Bundle size is within limits
- [ ] Security considerations are addressed

## Testing Requirements

### Unit Tests

- Write unit tests for all new functionality
- Maintain or improve test coverage
- Use descriptive test names and assertions

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_feature_functionality() {
        // Arrange
        let input = "test input";
        
        // Act
        let result = your_function(input);
        
        // Assert
        assert_eq!(result, expected_output);
    }
}
```

### Integration Tests

- Test component interactions
- Verify UI behavior
- Test error scenarios

### Running Tests

```bash
# Run all tests
cargo test --all

# Run tests with coverage
cargo install cargo-llvm-cov
cargo llvm-cov --all-features --workspace

# Run specific test
cargo test test_name
```

## Performance Guidelines

### Bundle Size Limits

The CI/CD pipeline enforces these limits:

- **WASM Bundle**: 2MB (2048KB)
- **JavaScript Bundle**: 512KB  
- **CSS Bundle**: 256KB

### Optimization Techniques

1. **Rust/WASM Optimization**
   ```bash
   # Use release profile optimizations in Cargo.toml
   [profile.release]
   opt-level = "z"
   lto = true
   codegen-units = 1
   panic = "abort"
   strip = true
   ```

2. **Asset Optimization**
   ```bash
   # Optimize SVG assets
   ./build.sh optimize-svg
   
   # Optimize WASM
   ./build.sh optimize-wasm
   ```

3. **CSS Optimization**
   - Use Tailwind's purge feature
   - Minimize custom CSS
   - Use PostCSS optimizations

## Security Considerations

### Dependency Management

- Keep dependencies up to date
- Review security advisories regularly
- Use `cargo audit` to check for vulnerabilities

### Code Security

- Validate all user inputs
- Sanitize data before display
- Use secure coding practices
- Avoid unsafe Rust code unless necessary

### Security Scanning

The CI/CD pipeline includes:
- Rust dependency audit (`cargo audit`)
- License compliance checking (`cargo deny`)
- Node.js dependency audit (`pnpm audit`)

## Pull Request Process

### Before Creating a PR

1. **Sync with latest dev**
   ```bash
   git checkout dev
   git pull origin dev
   git checkout your-feature-branch
   git rebase dev
   ```

2. **Run local checks**
   ```bash
   # Format code
   cargo fmt
   
   # Check for warnings
   cargo clippy
   
   # Run tests
   cargo test --all
   
   # Build project
   pnpm build
   ```

### PR Requirements

- [ ] **Title**: Use conventional commit format (`feat:`, `fix:`, `docs:`, etc.)
- [ ] **Description**: Clearly describe changes and motivation
- [ ] **Tests**: Include tests for new functionality
- [ ] **Documentation**: Update relevant documentation
- [ ] **Breaking Changes**: Document any breaking changes
- [ ] **Screenshots**: Include screenshots for UI changes

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
- [ ] Bundle size within limits
```

## Branch Protection Rules

### Required Status Checks

All PRs must pass these automated checks:

1. **Lint and Code Quality**
   - Rust formatting and linting
   - CSS/PostCSS validation

2. **Security and Vulnerability Scan**
   - Dependency security audit
   - License compliance check

3. **Test and Coverage**
   - Unit test execution
   - Code coverage analysis

4. **Build and Performance Analysis**
   - Successful build verification
   - Bundle size regression testing

### Review Requirements

- **Dev branch**: 1 reviewer required
- **Main branch**: 1 reviewer required + all status checks
- **Conversation resolution**: All discussions must be resolved

## Getting Help

### Resources

- [Project Documentation](./docs/)
- [Rust Book](https://doc.rust-lang.org/book/)
- [Yew Documentation](https://yew.rs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)

### Communication

- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Discussions**: Use GitHub Discussions for questions and ideas
- **Security**: Report security issues privately via email

### Common Issues

**Build Failures:**
- Check Rust toolchain version
- Verify Node.js dependencies are installed
- Ensure Trunk is installed and up to date

**Test Failures:**
- Run tests locally first
- Check for environment-specific issues
- Verify test data and expectations

**Bundle Size Issues:**
- Analyze bundle composition
- Remove unused dependencies
- Optimize asset sizes

Thank you for contributing to Yew IT Tools! 🦀✨
