---
name: Performance Issue
about: Report performance problems or bundle size regressions
title: '[PERFORMANCE] '
labels: ['performance', 'needs-triage']
assignees: ''
---

## Performance Issue Description
A clear and concise description of the performance problem.

## Performance Metrics
**Current Performance:**
- Bundle size: [e.g. WASM: 1.5MB, JS: 300KB, CSS: 150KB]
- Load time: [e.g. 3.2 seconds]
- Time to interactive: [e.g. 4.1 seconds]
- Lighthouse score: [e.g. Performance: 75]

**Expected Performance:**
- Bundle size: [e.g. WASM: <1MB, JS: <200KB, CSS: <100KB]
- Load time: [e.g. <2 seconds]
- Time to interactive: [e.g. <3 seconds]
- Lighthouse score: [e.g. Performance: >90]

## Steps to Reproduce
1. Go to [URL or page]
2. Perform [action]
3. Measure [metric]
4. Observe [result]

## Environment
- Device: [e.g. Desktop, Mobile]
- OS: [e.g. Windows 10, macOS 12, iOS 15]
- Browser: [e.g. Chrome 114, Firefox 115, Safari 16]
- Network: [e.g. Fast 3G, Slow 3G, WiFi]
- CPU throttling: [e.g. 4x slowdown]

## Performance Analysis
**Bundle Analysis:**
```
# Paste output from bundle analysis tools
pnpm analyze
cargo bloat --release --crates
```

**Network Analysis:**
- Total transfer size: [e.g. 2.1MB]
- Compressed size: [e.g. 800KB]
- Number of requests: [e.g. 15]

**Runtime Performance:**
- JavaScript execution time: [e.g. 1.2s]
- WASM compilation time: [e.g. 300ms]
- Rendering time: [e.g. 500ms]

## Potential Causes
- [ ] Large dependencies
- [ ] Unoptimized WASM
- [ ] Inefficient algorithms
- [ ] Memory leaks
- [ ] Excessive DOM manipulation
- [ ] Large assets (images, fonts)
- [ ] Blocking resources
- [ ] Other: [describe]

## Suggested Solutions
1. [Solution 1 with expected impact]
2. [Solution 2 with expected impact]
3. [Solution 3 with expected impact]

## Impact Assessment
- [ ] Affects all users
- [ ] Affects mobile users primarily
- [ ] Affects slow network connections
- [ ] Affects low-end devices
- [ ] Affects specific features: [list features]

## Additional Context
Add any other context about the performance issue here, including:
- Performance profiling screenshots
- Network waterfall charts
- Memory usage graphs
- CPU usage data

## Checklist
- [ ] I have measured the performance issue objectively
- [ ] I have tested on multiple devices/browsers
- [ ] I have provided specific metrics
- [ ] I have suggested potential solutions
- [ ] I have assessed the impact on users
