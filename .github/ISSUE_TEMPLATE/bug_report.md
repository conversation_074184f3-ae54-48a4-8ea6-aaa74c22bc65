---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. <PERSON>roll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment
**Desktop:**
- OS: [e.g. iOS]
- Browser: [e.g. chrome, safari]
- Version: [e.g. 22]

**Mobile:**
- Device: [e.g. iPhone6]
- OS: [e.g. iOS8.1]
- Browser: [e.g. stock browser, safari]
- Version: [e.g. 22]

## Build Information
- Rust version: [e.g. 1.70.0]
- Trunk version: [e.g. 0.17.0]
- Node.js version: [e.g. 18.16.0]
- pnpm version: [e.g. 8.6.0]

## Console Errors
If applicable, paste any console errors or logs here:
```
Paste console output here
```

## Additional Context
Add any other context about the problem here.

## Checklist
- [ ] I have searched for existing issues
- [ ] I have provided all the requested information
- [ ] I have tested this on the latest version
- [ ] I have included console errors (if any)
