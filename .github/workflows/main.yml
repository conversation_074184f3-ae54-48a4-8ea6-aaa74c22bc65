name: Main CI/CD Pipeline

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]
    types: [opened, synchronize, reopened, ready_for_review]
  # Allow manual triggering
  workflow_dispatch:

# Prevent multiple runs of the same workflow on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always
  RUSTFLAGS: "-D warnings"
  # Bundle size thresholds for regression testing
  MAX_WASM_SIZE_KB: 2048  # 2MB threshold for WASM bundle
  MAX_JS_SIZE_KB: 512     # 512KB threshold for JS bundle
  MAX_CSS_SIZE_KB: 256    # 256KB threshold for CSS bundle

jobs:
  # Lint and check code quality
  lint:
    name: Lint and Code Quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy, rustfmt

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Check Rust formatting
        run: cargo fmt --all -- --check

      - name: Run Rust clippy
        run: cargo clippy --all-targets --all-features -- -D warnings

      - name: Run cargo check
        run: cargo check --all-targets --all-features

      - name: Setup Node.js for frontend linting
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: Install Node.js dependencies
        run: pnpm install

      - name: Lint CSS/PostCSS configuration
        run: |
          # Check if PostCSS is available and config is valid
          if command -v postcss >/dev/null 2>&1; then
            echo "✅ PostCSS is available"
            postcss --version || echo "⚠️ PostCSS version check failed, but continuing..."
          else
            echo "⚠️ PostCSS not found in PATH, checking via npx..."
            npx postcss --version || echo "⚠️ PostCSS not available via npx, but continuing..."
          fi

          # Validate PostCSS configuration file exists
          if [ -f "postcss.config.mjs" ]; then
            echo "✅ PostCSS configuration file found"
            # Try to validate the config by running a simple postcss command
            echo "/* test */" | npx postcss --config postcss.config.mjs || echo "⚠️ PostCSS config validation failed, but continuing..."
          else
            echo "⚠️ PostCSS configuration file not found"
          fi

          # Validate Tailwind CSS configuration
          if command -v tailwindcss >/dev/null 2>&1; then
            echo "✅ Tailwind CSS is available"
            tailwindcss --help > /dev/null || echo "⚠️ Tailwind CSS help failed, but continuing..."
          else
            echo "⚠️ Tailwind CSS not found in PATH, checking via npx..."
            npx tailwindcss --help > /dev/null || echo "⚠️ Tailwind CSS not available via npx, but continuing..."
          fi

  # Security scanning
  security:
    name: Security and Vulnerability Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Run cargo-audit (Rust dependencies)
        run: cargo audit

      - name: Install cargo-deny
        run: cargo install cargo-deny

      - name: Run cargo-deny (License and security checks)
        run: cargo deny check

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: Install Node.js dependencies
        run: pnpm install

      - name: Run npm audit (Node.js dependencies)
        run: |
          # Run audit and capture output
          if ! pnpm audit --audit-level moderate; then
            echo "::warning::Found vulnerabilities in Node.js dependencies"
            pnpm audit --audit-level moderate --json > audit-results.json || true
            echo "::notice::Audit results saved to audit-results.json"
          fi

      - name: Upload audit results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: security-audit-results
          path: |
            audit-results.json
          if-no-files-found: ignore

  # Test Rust code
  test:
    name: Test and Coverage
    runs-on: ubuntu-latest
    needs: [lint, security]
    steps:
      - uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          components: llvm-tools-preview

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Install cargo-llvm-cov
        run: cargo install cargo-llvm-cov

      - name: Run unit tests
        run: cargo test --all --verbose

      - name: Run tests with coverage
        run: cargo llvm-cov --all-features --workspace --lcov --output-path lcov.info

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          files: lcov.info
          fail_ci_if_error: false
          verbose: true

      - name: Upload coverage artifact
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: lcov.info

  # Build and package with performance monitoring
  build:
    name: Build and Performance Analysis
    runs-on: ubuntu-latest
    needs: test
    steps:
      - uses: actions/checkout@v4
        with:
          # Fetch more history for bundle size comparison
          fetch-depth: 0

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: wasm32-unknown-unknown

      - name: Install Trunk
        uses: jetli/trunk-action@v0.5.0
        with:
          version: 'latest'

      - name: Install wasm-bindgen-cli
        run: cargo install wasm-bindgen-cli

      - name: Install wasm-opt
        run: |
          curl -L https://github.com/WebAssembly/binaryen/releases/download/version_116/binaryen-version_116-x86_64-linux.tar.gz | tar xz
          sudo cp binaryen-version_116/bin/wasm-opt /usr/local/bin/
          wasm-opt --version

      - name: Install svgo
        run: npm install -g svgo

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Cache pnpm dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install Node.js dependencies
        run: pnpm install

      - name: Make build script executable
        run: |
          chmod +x ./build.sh

      - name: Build project
        run: pnpm build

      - name: Analyze bundle sizes and check for regressions
        run: |
          echo "=== Bundle Size Analysis ==="

          # Get current bundle sizes in KB
          WASM_SIZE_KB=$(find dist -name "*.wasm" -exec du -k {} \; | awk '{sum+=$1} END {print sum}')
          JS_SIZE_KB=$(find dist -name "*.js" -exec du -k {} \; | awk '{sum+=$1} END {print sum}')
          CSS_SIZE_KB=$(find dist -name "*.css" -exec du -k {} \; | awk '{sum+=$1} END {print sum}')

          echo "Current bundle sizes:"
          echo "- WASM: ${WASM_SIZE_KB}KB"
          echo "- JS: ${JS_SIZE_KB}KB"
          echo "- CSS: ${CSS_SIZE_KB}KB"

          # Check against thresholds
          REGRESSION_DETECTED=false

          if [ "$WASM_SIZE_KB" -gt "$MAX_WASM_SIZE_KB" ]; then
            echo "::error::WASM bundle size ($WASM_SIZE_KB KB) exceeds threshold ($MAX_WASM_SIZE_KB KB)"
            REGRESSION_DETECTED=true
          fi

          if [ "$JS_SIZE_KB" -gt "$MAX_JS_SIZE_KB" ]; then
            echo "::error::JS bundle size ($JS_SIZE_KB KB) exceeds threshold ($MAX_JS_SIZE_KB KB)"
            REGRESSION_DETECTED=true
          fi

          if [ "$CSS_SIZE_KB" -gt "$MAX_CSS_SIZE_KB" ]; then
            echo "::error::CSS bundle size ($CSS_SIZE_KB KB) exceeds threshold ($MAX_CSS_SIZE_KB KB)"
            REGRESSION_DETECTED=true
          fi

          # Create bundle size report using jq for safer JSON generation
          TOTAL_SIZE_KB=$((WASM_SIZE_KB + JS_SIZE_KB + CSS_SIZE_KB))

          jq -n \
            --argjson wasm_size_kb "$WASM_SIZE_KB" \
            --argjson js_size_kb "$JS_SIZE_KB" \
            --argjson css_size_kb "$CSS_SIZE_KB" \
            --argjson total_size_kb "$TOTAL_SIZE_KB" \
            --argjson wasm_max_kb "$MAX_WASM_SIZE_KB" \
            --argjson js_max_kb "$MAX_JS_SIZE_KB" \
            --argjson css_max_kb "$MAX_CSS_SIZE_KB" \
            --argjson regression_detected "$REGRESSION_DETECTED" \
            '{
              wasm_size_kb: $wasm_size_kb,
              js_size_kb: $js_size_kb,
              css_size_kb: $css_size_kb,
              total_size_kb: $total_size_kb,
              thresholds: {
                wasm_max_kb: $wasm_max_kb,
                js_max_kb: $js_max_kb,
                css_max_kb: $css_max_kb
              },
              regression_detected: $regression_detected
            }' > bundle-size-report.json

          if [ "$REGRESSION_DETECTED" = true ]; then
            echo "::warning::Bundle size regression detected! Please optimize your code."
            exit 1
          fi

          echo "✅ All bundle sizes are within acceptable limits"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/
          if-no-files-found: error

      - name: Generate CI summary
        run: |
          # Create a comprehensive CI summary using jq for safer JSON generation
          TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)
          WASM_SIZE_KB=$(du -k dist/*.wasm 2>/dev/null | cut -f1 | head -1 || echo 0)
          JS_SIZE_KB=$(du -k dist/*.js 2>/dev/null | cut -f1 | head -1 || echo 0)
          CSS_SIZE_KB=$(du -k dist/*.css 2>/dev/null | cut -f1 | head -1 || echo 0)
          TOTAL_SIZE_KB=$(du -sk dist/ | cut -f1)
          PR_NUMBER="${{ github.event.pull_request.number || 'null' }}"

          # Use jq to create JSON safely without sed replacement issues
          jq -n \
            --arg timestamp "$TIMESTAMP" \
            --arg workflow_run_id "${{ github.run_id }}" \
            --arg commit_sha "${{ github.sha }}" \
            --arg pr_number "$PR_NUMBER" \
            --arg branch "${{ github.ref_name }}" \
            --argjson wasm_size_kb "$WASM_SIZE_KB" \
            --argjson js_size_kb "$JS_SIZE_KB" \
            --argjson css_size_kb "$CSS_SIZE_KB" \
            --argjson total_size_kb "$TOTAL_SIZE_KB" \
            '{
              timestamp: $timestamp,
              workflow_run_id: $workflow_run_id,
              commit_sha: $commit_sha,
              pr_number: $pr_number,
              branch: $branch,
              build_status: "success",
              bundle_analysis: {
                wasm_size_kb: $wasm_size_kb,
                js_size_kb: $js_size_kb,
                css_size_kb: $css_size_kb,
                total_size_kb: $total_size_kb
              },
              quality_checks: {
                cargo_fmt: "passed",
                cargo_clippy: "passed",
                cargo_check: "passed",
                security_audit: "passed"
              }
            }' > ci-summary.json

      - name: Upload bundle size report
        uses: actions/upload-artifact@v4
        with:
          name: bundle-size-report
          path: |
            bundle-size-report.json
            ci-summary.json

      - name: Detailed build analysis
        run: |
          echo "=== Detailed Build Analysis ==="
          echo "Total size: $(du -sh dist | cut -f1)"
          echo "WASM files: $(find dist -name "*.wasm" | wc -l)"
          echo "JS files: $(find dist -name "*.js" | wc -l)"
          echo "CSS files: $(find dist -name "*.css" | wc -l)"
          echo ""
          echo "Compressed sizes:"
          if [ -f dist/*.wasm.gz ]; then
            echo "WASM (gzipped): $(find dist -name "*.wasm.gz" -exec du -ch {} + | grep total | cut -f1)"
          fi
          if [ -f dist/*.js.gz ]; then
            echo "JS (gzipped): $(find dist -name "*.js.gz" -exec du -ch {} + | grep total | cut -f1)"
          fi
          if [ -f dist/*.css.gz ]; then
            echo "CSS (gzipped): $(find dist -name "*.css.gz" -exec du -ch {} + | grep total | cut -f1)"
          fi

  # Deploy to GitHub Pages (only from main branch)
  deploy-production:
    name: Deploy to Production (GitHub Pages)
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' && github.event_name != 'pull_request'
    permissions:
      contents: read
      pages: write
      id-token: write
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: wasm32-unknown-unknown

      - name: Install Trunk
        uses: jetli/trunk-action@v0.5.0
        with:
          version: 'latest'

      - name: Install wasm-bindgen-cli
        run: cargo install wasm-bindgen-cli

      - name: Install wasm-opt
        run: |
          curl -L https://github.com/WebAssembly/binaryen/releases/download/version_116/binaryen-version_116-x86_64-linux.tar.gz | tar xz
          sudo cp binaryen-version_116/bin/wasm-opt /usr/local/bin/
          wasm-opt --version

      - name: Install svgo
        run: npm install -g svgo

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Cache pnpm dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install Node.js dependencies
        run: pnpm install

      - name: Setup GitHub Pages
        uses: actions/configure-pages@v5

      - name: Build for GitHub Pages
        run: |
          # Make build script executable
          chmod +x ./build.sh
          # Use our unified build script for GitHub Pages
          ./build.sh github-pages

      - name: Upload GitHub Pages artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: dist

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

      - name: Log deployment URL
        run: |
          echo "Deployed to ${{ steps.deployment.outputs.page_url }}"

      - name: Add deployment summary
        run: |
          echo "## Production Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "✅ Successfully deployed to [GitHub Pages](${{ steps.deployment.outputs.page_url }})" >> $GITHUB_STEP_SUMMARY
          echo "### Build Information" >> $GITHUB_STEP_SUMMARY
          echo "- Deployment URL: ${{ steps.deployment.outputs.page_url }}" >> $GITHUB_STEP_SUMMARY
          echo "- Deployment Time: $(date)" >> $GITHUB_STEP_SUMMARY

  # Create a deployment summary for dev branch
  dev-summary:
    name: Dev Branch Summary
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/dev' && github.event_name != 'pull_request'
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Analyze build output
        run: |
          echo "Build output analysis for dev branch:"
          echo "-------------------------------------"
          echo "Total size: $(du -sh dist | cut -f1)"
          echo "WASM size: $(find dist -name "*.wasm" -exec du -h {} \; | cut -f1) ($(find dist -name "*.wasm" | wc -l) files)"
          echo "JS size: $(find dist -name "*.js" -exec du -h {} \; | cut -f1) ($(find dist -name "*.js" | wc -l) files)"
          echo "CSS size: $(find dist -name "*.css" -exec du -h {} \; | cut -f1) ($(find dist -name "*.css" | wc -l) files)"
          echo "-------------------------------------"

      - name: Add dev summary
        run: |
          echo "## Dev Branch Build Summary" >> $GITHUB_STEP_SUMMARY
          echo "✅ Successfully built dev branch" >> $GITHUB_STEP_SUMMARY
          echo "### Build Information" >> $GITHUB_STEP_SUMMARY
          echo "- Branch: dev" >> $GITHUB_STEP_SUMMARY
          echo "- Build Time: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- Total Size: $(du -sh dist | cut -f1)" >> $GITHUB_STEP_SUMMARY
