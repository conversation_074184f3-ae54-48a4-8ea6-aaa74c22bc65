## Description
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Dependency update

## Related Issues
Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Changes Made
- [ ] Change 1
- [ ] Change 2
- [ ] Change 3

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All existing tests pass

### Test Coverage
- Current coverage: [e.g. 85%]
- Coverage change: [e.g. +2%]

## Performance Impact
- [ ] No performance impact
- [ ] Performance improvement
- [ ] Potential performance regression (explain below)

### Bundle Size Analysis
- WASM size change: [e.g. +50KB / -20KB / no change]
- JS size change: [e.g. +10KB / -5KB / no change]
- CSS size change: [e.g. +2KB / -1KB / no change]
- Total size change: [e.g. +62KB / -26KB / no change]

## Security Considerations
- [ ] No security implications
- [ ] Security improvement
- [ ] Potential security impact (explain below)

### Security Checklist
- [ ] Input validation added/updated
- [ ] No sensitive data exposed
- [ ] Dependencies are secure
- [ ] No unsafe Rust code introduced

## Breaking Changes
- [ ] No breaking changes
- [ ] Breaking changes (list below)

### Breaking Changes List
1. [Breaking change 1 with migration guide]
2. [Breaking change 2 with migration guide]

## Documentation
- [ ] Code is self-documenting
- [ ] Inline comments added where necessary
- [ ] README updated
- [ ] API documentation updated
- [ ] Migration guide provided (for breaking changes)

## Screenshots (if applicable)
### Before
[Screenshot or description of current state]

### After
[Screenshot or description of new state]

## Checklist
### Code Quality
- [ ] Code follows the project's style guidelines
- [ ] Self-review of code completed
- [ ] Code is properly formatted (`cargo fmt`)
- [ ] No clippy warnings (`cargo clippy`)
- [ ] No compiler warnings

### Testing
- [ ] Tests pass locally (`cargo test`)
- [ ] New tests cover the changes
- [ ] Edge cases are tested
- [ ] Error handling is tested

### Performance
- [ ] Bundle size is within limits
- [ ] No performance regressions introduced
- [ ] Optimizations applied where appropriate

### Security
- [ ] Security implications considered
- [ ] Dependencies are up to date
- [ ] No sensitive information in code

### Documentation
- [ ] Code is well-documented
- [ ] Public APIs have rustdoc comments
- [ ] README updated if needed
- [ ] Breaking changes documented

## Additional Notes
Add any additional notes, concerns, or context for reviewers here.

## Reviewer Guidelines
Please ensure:
1. All status checks pass
2. Code follows project standards
3. Tests are comprehensive
4. Performance impact is acceptable
5. Security considerations are addressed
6. Documentation is adequate

---
**Note to Reviewers:** This PR requires approval from at least one maintainer and all automated checks must pass before merging.
