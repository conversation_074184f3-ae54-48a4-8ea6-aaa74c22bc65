# CODEOWNERS file for yew-it-tools
# This file defines who should review specific parts of the codebase
# See: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# Global owners - these users will be requested for review on all PRs
* @naoNao89

# Rust source code
/src/ @naoNao89
/Cargo.toml @naoNao89
/Cargo.lock @naoNao89

# Build and deployment configuration
/build.sh @naoNao89
/Trunk.toml @naoNao89
/Trunk.toml.* @naoNao89

# Frontend assets and configuration
/src/assets/ @naoNao89
/src/tailwind.css @naoNao89
/src/tailwind.config.css @naoNao89
/postcss.config.mjs @naoNao89
/package.json @naoNao89
/pnpm-lock.yaml @naoNao89

# CI/CD and GitHub configuration
/.github/ @naoNao89
/deny.toml @naoNao89

# Documentation
/README.md @naoNao89
/CONTRIBUTING.md @naoNao89
/docs/ @naoNao89

# Security-sensitive files
/deny.toml @naoNao89
/.github/workflows/ @naoNao89

# Component-specific ownership
/src/components/ @naoNao89
/src/layouts/ @naoNao89
/src/pages/ @naoNao89
/src/ui/ @naoNao89

# Core application files
/src/main.rs @naoNao89
/src/router.rs @naoNao89
/src/config.rs @naoNao89

# Tools and utilities
/src/tools/ @naoNao89
/src/utils/ @naoNao89

# State management
/src/stores/ @naoNao89
/src/themes.rs @naoNao89
