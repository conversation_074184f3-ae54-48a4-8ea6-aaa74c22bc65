#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Print with color
print_info() {
    echo -e "\033[0;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[0;33m[WARNING]\033[0m $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [TARGET] [OPTIONS] [COMMAND]"
    echo ""
    echo "Targets:"
    echo "  dev          - Development build (default)"
    echo "  release      - Production release build"
    echo "  github-pages - GitHub Pages deployment build"
    echo ""
    echo "Options:"
    echo "  --serve      - Serve the build after completion"
    echo "  --help       - Show this help message"
    echo ""
    echo "Commands:"
    echo "  css          - Build Tailwind CSS only"
    echo "  optimize-svg - Optimize SVG files only"
    echo "  optimize-wasm - Optimize WASM files only"
    echo ""
    echo "Examples:"
    echo "  $0                    # Development build"
    echo "  $0 release           # Production build"
    echo "  $0 github-pages      # GitHub Pages build"
    echo "  $0 dev --serve       # Development build and serve"
    echo "  $0 css               # Build Tailwind CSS only"
    echo "  $0 optimize-svg      # Optimize SVG files only"
    echo "  $0 optimize-wasm     # Optimize WASM files only"
}

# Function to build Tailwind CSS
build_tailwind_css() {
    # Build lock mechanism to prevent rapid successive builds
    BUILD_LOCK_FILE=".build-lock"
    BUILD_LOCK_TIMEOUT=2  # seconds

    # Check if a build is already in progress
    if [ -f "$BUILD_LOCK_FILE" ]; then
        # Cross-platform way to get file modification time
        if [ "$(uname)" = "Darwin" ]; then
            # macOS
            LOCK_TIME=$(stat -f %m "$BUILD_LOCK_FILE" 2>/dev/null || echo 0)
        else
            # Linux and others
            LOCK_TIME=$(stat -c %Y "$BUILD_LOCK_FILE" 2>/dev/null || echo 0)
        fi

        CURRENT_TIME=$(date +%s)
        TIME_DIFF=$((CURRENT_TIME - LOCK_TIME))

        if [ $TIME_DIFF -lt $BUILD_LOCK_TIMEOUT ]; then
            print_info "Build already in progress (locked $TIME_DIFF seconds ago), skipping..."
            return 0
        else
            print_warning "Stale build lock detected, removing..."
            rm -f "$BUILD_LOCK_FILE"
        fi
    fi

    # Create build lock
    touch "$BUILD_LOCK_FILE"

    # Ensure lock is removed on exit
    trap 'rm -f "$BUILD_LOCK_FILE"' EXIT

    print_info "Starting Tailwind CSS build..."

    # Create the output directories if they don't exist
    mkdir -p dist/assets/css dist/assets/icons

    # Determine NODE_ENV if not set
    if [ -z "$NODE_ENV" ]; then
        if [ -z "$TRUNK_SERVE_ENV" ]; then
            export NODE_ENV=production
        else
            export NODE_ENV=development
        fi
    fi

    # Check if source file exists
    if [ ! -f "./src/tailwind.css" ]; then
        print_error "Source file ./src/tailwind.css not found!"
        return 1
    fi

    # Check if postcss is available
    if ! command -v npx &> /dev/null; then
        print_error "npx command not found. Please install Node.js and npm/pnpm."
        return 1
    fi

    # Determine if we need to rebuild based on file modification times
    REBUILD=true
    if [ -f "./dist/tailwind.css" ] && [ "$NODE_ENV" = "production" ]; then
        if [ "$(uname)" = "Darwin" ]; then
            # macOS
            SRC_MTIME=$(stat -f %m "./src/tailwind.css" 2>/dev/null)
            CONFIG_MTIME=$(stat -f %m "./src/tailwind.config.css" 2>/dev/null)
            POSTCSS_MTIME=$(stat -f %m "./postcss.config.mjs" 2>/dev/null)
            DEST_MTIME=$(stat -f %m "./dist/tailwind.css" 2>/dev/null)
        else
            # Linux and others
            SRC_MTIME=$(stat -c %Y "./src/tailwind.css" 2>/dev/null)
            CONFIG_MTIME=$(stat -c %Y "./src/tailwind.config.css" 2>/dev/null)
            POSTCSS_MTIME=$(stat -c %Y "./postcss.config.mjs" 2>/dev/null)
            DEST_MTIME=$(stat -c %Y "./dist/tailwind.css" 2>/dev/null)
        fi

        # Only rebuild if source files are newer than the output
        if [ "$DEST_MTIME" -gt "$SRC_MTIME" ] && [ "$DEST_MTIME" -gt "$CONFIG_MTIME" ] && [ "$DEST_MTIME" -gt "$POSTCSS_MTIME" ]; then
            print_info "Tailwind CSS is up to date, skipping build"
            REBUILD=false
        fi
    fi

    # Build Tailwind CSS with PostCSS and Autoprefixer if needed
    if [ "$REBUILD" = true ]; then
        print_info "Building Tailwind CSS in $NODE_ENV mode..."

        # Record start time for performance measurement
        START_TIME=$(date +%s.%N)

        if npx postcss ./src/tailwind.css -o ./dist/tailwind.css; then
            # Calculate build time
            END_TIME=$(date +%s.%N)
            BUILD_TIME=$(echo "$END_TIME - $START_TIME" | bc)

            # Get file size
            if [ "$(uname)" = "Darwin" ]; then
                # macOS
                FILE_SIZE=$(stat -f %z "./dist/tailwind.css" 2>/dev/null)
            else
                # Linux and others
                FILE_SIZE=$(stat -c %s "./dist/tailwind.css" 2>/dev/null)
            fi

            # Convert to human-readable size
            HUMAN_SIZE=$(echo "$FILE_SIZE" | awk '{ split("B KB MB GB TB PB", unit); s=1; while($1>1024){$1/=1024; s++} printf "%.1f %s", $1, unit[s]}')

            print_success "Tailwind CSS built successfully in ${BUILD_TIME}s (${HUMAN_SIZE})"
        else
            print_error "Failed to build Tailwind CSS"
            return 1
        fi
    fi

    # Copy assets only in build mode (not in development mode to avoid rebuild loops)
    if [ -z "$TRUNK_SERVE_ENV" ]; then
        print_info "Copying assets..."

        # Function to copy assets efficiently with caching
        copy_assets() {
            local src="$1"
            local dest="$2"
            local type="$3"
            local cache_file=".$(echo "$type" | tr '[:upper:]' '[:lower:]')-copy-cache"

            if [ ! -d "$src" ]; then
                print_info "No $type assets to copy (directory doesn't exist)"
                return
            fi

            if [ ! "$(ls -A "$src" 2>/dev/null)" ]; then
                print_info "No $type assets to copy (directory is empty)"
                return
            fi

            # Create destination directory
            mkdir -p "$dest"

            # Try rsync first (most efficient), then cp as fallback
            if command -v rsync &>/dev/null; then
                if rsync -a --update --checksum "$src/" "$dest/" 2>/dev/null; then
                    print_success "Copied $type assets with rsync"
                else
                    # Fallback to cp
                    if cp -r "$src"/* "$dest"/ 2>/dev/null; then
                        print_success "Copied $type assets with cp"
                    else
                        print_warning "Some $type assets could not be copied"
                    fi
                fi
            else
                # Use cp if rsync is not available
                if cp -r "$src"/* "$dest"/ 2>/dev/null; then
                    print_success "Copied $type assets with cp"
                else
                    print_warning "Some $type assets could not be copied"
                fi
            fi
        }

        # Copy CSS and icon assets
        copy_assets "src/assets/css" "dist/assets/css" "CSS"
        copy_assets "src/assets/icons" "dist/assets/icons" "icon"

        # Copy hero gradient SVG if it exists
        if [ -f "src/assets/hero-gradient.svg" ]; then
            mkdir -p dist/assets
            if cp "src/assets/hero-gradient.svg" "dist/assets/" 2>/dev/null; then
                print_success "Copied hero gradient SVG"
            else
                print_warning "Could not copy hero gradient SVG"
            fi
        fi
    else
        print_info "Development mode: Skipping asset copying to avoid rebuild loops"
    fi

    print_success "Tailwind CSS build completed successfully"
    return 0
}

# Function to optimize SVG files
optimize_svg() {
    # Check if we're in development mode (TRUNK_SERVE_ENV is set)
    if [ -n "$TRUNK_SERVE_ENV" ] && [ "$TRUNK_RELEASE" != "true" ]; then
        print_info "Development mode: Skipping SVG optimization to avoid rebuild loops"
        return 0
    fi

    # SVG optimization lock mechanism to prevent rapid successive runs
    SVG_LOCK_FILE=".svg-optimization-lock"
    SVG_LOCK_TIMEOUT=1  # seconds

    # Check if SVG optimization is already in progress
    if [ -f "$SVG_LOCK_FILE" ]; then
        # Cross-platform way to get file modification time
        if [ "$(uname)" = "Darwin" ]; then
            # macOS
            LOCK_TIME=$(stat -f %m "$SVG_LOCK_FILE" 2>/dev/null || echo 0)
        else
            # Linux and others
            LOCK_TIME=$(stat -c %Y "$SVG_LOCK_FILE" 2>/dev/null || echo 0)
        fi

        CURRENT_TIME=$(date +%s)
        TIME_DIFF=$((CURRENT_TIME - LOCK_TIME))

        if [ $TIME_DIFF -lt $SVG_LOCK_TIMEOUT ]; then
            print_info "SVG optimization already in progress (locked $TIME_DIFF seconds ago), skipping..."
            return 0
        else
            print_warning "Stale SVG optimization lock detected, removing..."
            rm -f "$SVG_LOCK_FILE"
        fi
    fi

    # Create SVG optimization lock
    touch "$SVG_LOCK_FILE"

    # Ensure lock is removed on exit
    trap 'rm -f "$SVG_LOCK_FILE"' EXIT

    print_info "Starting SVG optimization..."

    # Check if svgo is installed
    if ! command -v svgo &> /dev/null; then
        print_warning "svgo not found. Skipping SVG optimization."
        print_info "To enable SVG optimization, install SVGO with: pnpm install -g svgo"
        return 0
    fi

    # Check if the icons directory exists
    if [ ! -d "src/assets/icons" ]; then
        print_info "Icons directory not found. Skipping SVG optimization."
        return 0
    fi

    # Find all SVG files in the src/assets/icons directory
    SVG_FILES=$(find src/assets/icons -name "*.svg" 2>/dev/null || echo "")

    if [ -z "$SVG_FILES" ]; then
        print_info "No SVG files found in src/assets/icons directory."
        return 0
    fi

    SVG_COUNT=$(echo "$SVG_FILES" | wc -l | tr -d '[:space:]')
    print_info "Found $SVG_COUNT SVG files to optimize."

    # Create a cache file to track optimized SVGs
    CACHE_FILE=".svg-optimization-cache"
    touch "$CACHE_FILE" 2>/dev/null

    # Track optimization statistics
    OPTIMIZED_COUNT=0
    SKIPPED_COUNT=0
    FAILED_COUNT=0

    # Optimize each SVG file
    for SVG_FILE in $SVG_FILES; do
        # Get the hash of the SVG file to check if it's already been optimized
        if command -v md5sum &> /dev/null; then
            SVG_HASH=$(md5sum "$SVG_FILE" 2>/dev/null | cut -d ' ' -f 1)
        elif command -v md5 &> /dev/null; then
            # macOS uses md5 instead of md5sum
            SVG_HASH=$(md5 -q "$SVG_FILE" 2>/dev/null)
        else
            # Fallback to file size and modification time if no hash tool available
            SVG_SIZE=$(stat -c %s "$SVG_FILE" 2>/dev/null || stat -f %z "$SVG_FILE" 2>/dev/null)
            SVG_MTIME=$(stat -c %Y "$SVG_FILE" 2>/dev/null || stat -f %m "$SVG_FILE" 2>/dev/null)
            SVG_HASH="${SVG_SIZE}-${SVG_MTIME}"
        fi

        SVG_CACHE_ENTRY="${SVG_FILE}:${SVG_HASH}"

        # Skip if this exact file has already been optimized
        if grep -q "$SVG_CACHE_ENTRY" "$CACHE_FILE" 2>/dev/null; then
            print_info "Skipping already optimized $SVG_FILE"
            SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
            continue
        fi

        print_info "Optimizing $SVG_FILE..."

        # Skip if file doesn't exist
        if [ ! -f "$SVG_FILE" ]; then
            print_warning "File $SVG_FILE not found, skipping."
            FAILED_COUNT=$((FAILED_COUNT + 1))
            continue
        fi

        # Get original file size
        ORIG_SIZE=$(stat -c %s "$SVG_FILE" 2>/dev/null || stat -f %z "$SVG_FILE" 2>/dev/null)

        # Create a temporary file for optimization
        TEMP_FILE=$(mktemp)

        # Copy the original file to the temporary file
        cp "$SVG_FILE" "$TEMP_FILE"

        # Optimize the SVG file with svgo
        if svgo -i "$TEMP_FILE" -o "$TEMP_FILE.opt" --pretty 2>/dev/null; then
            # If optimization was successful, replace the original file
            if cmp -s "$TEMP_FILE.opt" "$SVG_FILE"; then
                print_info "No changes needed for $SVG_FILE"
                SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
            else
                if cp "$TEMP_FILE.opt" "$SVG_FILE" 2>/dev/null; then
                    # Get new file size and calculate reduction
                    NEW_SIZE=$(stat -c %s "$SVG_FILE" 2>/dev/null || stat -f %z "$SVG_FILE" 2>/dev/null)
                    REDUCTION=$((ORIG_SIZE - NEW_SIZE))
                    PERCENT=$(awk "BEGIN {printf \"%.1f\", ($REDUCTION / $ORIG_SIZE) * 100}")

                    print_success "Optimized $SVG_FILE - reduced by $REDUCTION bytes ($PERCENT%)"
                    OPTIMIZED_COUNT=$((OPTIMIZED_COUNT + 1))
                else
                    print_warning "Could not update $SVG_FILE"
                    FAILED_COUNT=$((FAILED_COUNT + 1))
                fi
            fi

            # Mark this file as optimized
            echo "$SVG_CACHE_ENTRY" >> "$CACHE_FILE"
        else
            print_warning "SVG optimization failed for $SVG_FILE, skipping."
            FAILED_COUNT=$((FAILED_COUNT + 1))
        fi

        # Clean up temporary files
        rm -f "$TEMP_FILE" "$TEMP_FILE.opt" 2>/dev/null
    done

    print_success "SVG optimization completed successfully."
    print_info "Optimization summary:"
    print_info "- Total SVG files: $SVG_COUNT"
    print_info "- Optimized: $OPTIMIZED_COUNT"
    print_info "- Skipped (already optimized): $SKIPPED_COUNT"
    print_info "- Failed: $FAILED_COUNT"

    return 0
}

# Function to optimize WASM files
optimize_wasm() {
    # Check if we're in development mode (TRUNK_SERVE_ENV is set)
    if [ -n "$TRUNK_SERVE_ENV" ] && [ "$TRUNK_RELEASE" != "true" ]; then
        print_info "Development mode: Skipping WASM optimization to avoid rebuild loops"
        return 0
    fi

    print_info "Starting WASM optimization..."

    # Check if wasm-opt is installed
    if ! command -v wasm-opt &> /dev/null; then
        print_warning "wasm-opt not found. Skipping WASM optimization."
        print_info "To enable WASM optimization, install binaryen with your package manager:"
        print_info "  - macOS: brew install binaryen"
        print_info "  - Ubuntu/Debian: apt install binaryen"
        print_info "  - Arch Linux: pacman -S binaryen"
        print_info "  - Fedora: dnf install binaryen"
        print_info "  - Manual: https://github.com/WebAssembly/binaryen/releases"
        return 0
    fi

    # Check if dist directory exists
    if [ ! -d "dist" ]; then
        print_warning "Dist directory not found. Skipping WASM optimization."
        return 0
    fi

    # Find all wasm files in the dist directory
    WASM_FILES=$(find dist -name "*.wasm" 2>/dev/null)

    if [ -z "$WASM_FILES" ]; then
        print_warning "No WASM files found in dist directory."
        return 0
    fi

    WASM_COUNT=$(echo "$WASM_FILES" | wc -l | tr -d '[:space:]')
    print_info "Found $WASM_COUNT WASM file(s) to optimize."

    # Create a cache file to track optimized WASM files
    CACHE_FILE=".wasm-optimization-cache"
    touch "$CACHE_FILE" 2>/dev/null

    # Track optimization statistics
    OPTIMIZED_COUNT=0
    SKIPPED_COUNT=0
    FAILED_COUNT=0
    TOTAL_ORIGINAL_BYTES=0
    TOTAL_OPTIMIZED_BYTES=0
    TOTAL_GZIP_BYTES=0
    TOTAL_BROTLI_BYTES=0

    # Process each WASM file
    for WASM_FILE in $WASM_FILES; do
        print_info "Processing WASM file: $WASM_FILE"

        # Get original file size in bytes for precise comparison
        ORIGINAL_BYTES=$(stat -c %s "$WASM_FILE" 2>/dev/null || stat -f %z "$WASM_FILE" 2>/dev/null)
        ORIGINAL_SIZE=$(du -h "$WASM_FILE" 2>/dev/null | cut -f1)
        print_info "Original size: $ORIGINAL_SIZE ($ORIGINAL_BYTES bytes)"

        TOTAL_ORIGINAL_BYTES=$((TOTAL_ORIGINAL_BYTES + ORIGINAL_BYTES))

        # Get the hash of the WASM file to check if it's already been optimized
        if command -v md5sum &> /dev/null; then
            WASM_HASH=$(md5sum "$WASM_FILE" 2>/dev/null | cut -d ' ' -f 1)
        elif command -v md5 &> /dev/null; then
            # macOS uses md5 instead of md5sum
            WASM_HASH=$(md5 -q "$WASM_FILE" 2>/dev/null)
        else
            # Fallback to file size and modification time if no hash tool available
            WASM_SIZE=$(stat -c %s "$WASM_FILE" 2>/dev/null || stat -f %z "$WASM_FILE" 2>/dev/null)
            WASM_MTIME=$(stat -c %Y "$WASM_FILE" 2>/dev/null || stat -f %m "$WASM_FILE" 2>/dev/null)
            WASM_HASH="${WASM_SIZE}-${WASM_MTIME}"
        fi

        WASM_CACHE_ENTRY="${WASM_FILE}:${WASM_HASH}"

        # Skip if this exact file has already been optimized
        if grep -q "$WASM_CACHE_ENTRY" "$CACHE_FILE" 2>/dev/null; then
            print_info "Skipping already optimized WASM file"
            SKIPPED_COUNT=$((SKIPPED_COUNT + 1))

            # Still count the optimized size in our totals
            OPTIMIZED_BYTES=$(stat -c %s "$WASM_FILE" 2>/dev/null || stat -f %z "$WASM_FILE" 2>/dev/null)
            TOTAL_OPTIMIZED_BYTES=$((TOTAL_OPTIMIZED_BYTES + OPTIMIZED_BYTES))

            # Check for existing compressed versions
            if [ -f "${WASM_FILE}.gz" ]; then
                GZIP_BYTES=$(stat -c %s "${WASM_FILE}.gz" 2>/dev/null || stat -f %z "${WASM_FILE}.gz" 2>/dev/null)
                TOTAL_GZIP_BYTES=$((TOTAL_GZIP_BYTES + GZIP_BYTES))
            fi

            if [ -f "${WASM_FILE}.br" ]; then
                BROTLI_BYTES=$(stat -c %s "${WASM_FILE}.br" 2>/dev/null || stat -f %z "${WASM_FILE}.br" 2>/dev/null)
                TOTAL_BROTLI_BYTES=$((TOTAL_BROTLI_BYTES + BROTLI_BYTES))
            fi
        else
            # Create a temporary file for optimization
            TEMP_FILE=$(mktemp)

            # Copy the original wasm file to the temporary file
            cp "$WASM_FILE" "$TEMP_FILE" 2>/dev/null

            # Optimize the wasm file with wasm-opt
            print_info "Optimizing WASM file with wasm-opt..."
            if wasm-opt -Oz --enable-bulk-memory --enable-reference-types --enable-sign-ext --enable-nontrapping-float-to-int "$TEMP_FILE" -o "$TEMP_FILE.opt" 2>/dev/null; then
                # If optimization was successful, replace the original file
                if cmp -s "$TEMP_FILE.opt" "$WASM_FILE"; then
                    print_info "No changes needed for WASM file"
                    SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
                    OPTIMIZED_BYTES=$ORIGINAL_BYTES
                else
                    if cp "$TEMP_FILE.opt" "$WASM_FILE" 2>/dev/null; then
                        # Get new file size and calculate reduction
                        OPTIMIZED_BYTES=$(stat -c %s "$WASM_FILE" 2>/dev/null || stat -f %z "$WASM_FILE" 2>/dev/null)
                        REDUCTION=$((ORIGINAL_BYTES - OPTIMIZED_BYTES))
                        PERCENT=$(awk "BEGIN {printf \"%.1f\", ($REDUCTION / $ORIGINAL_BYTES) * 100}")
                        OPTIMIZED_SIZE=$(du -h "$WASM_FILE" 2>/dev/null | cut -f1)

                        print_success "Optimized WASM file - reduced by $REDUCTION bytes ($PERCENT%)"
                        print_success "New size: $OPTIMIZED_SIZE ($OPTIMIZED_BYTES bytes)"
                        OPTIMIZED_COUNT=$((OPTIMIZED_COUNT + 1))
                    else
                        print_warning "Could not update $WASM_FILE"
                        FAILED_COUNT=$((FAILED_COUNT + 1))
                        OPTIMIZED_BYTES=$ORIGINAL_BYTES
                    fi
                fi

                # Mark this file as optimized
                echo "$WASM_CACHE_ENTRY" >> "$CACHE_FILE"
            else
                print_warning "WASM optimization failed, using original file."
                FAILED_COUNT=$((FAILED_COUNT + 1))
                OPTIMIZED_BYTES=$ORIGINAL_BYTES
            fi

            TOTAL_OPTIMIZED_BYTES=$((TOTAL_OPTIMIZED_BYTES + OPTIMIZED_BYTES))

            # Clean up temporary files
            rm -f "$TEMP_FILE" "$TEMP_FILE.opt" 2>/dev/null
        fi

        # Compress the optimized wasm file with gzip
        print_info "Compressing WASM file with gzip..."
        if gzip -9 -f -k "$WASM_FILE" 2>/dev/null; then
            GZIP_BYTES=$(stat -c %s "${WASM_FILE}.gz" 2>/dev/null || stat -f %z "${WASM_FILE}.gz" 2>/dev/null)
            GZIP_SIZE=$(du -h "${WASM_FILE}.gz" 2>/dev/null | cut -f1)
            GZIP_PERCENT=$(awk "BEGIN {printf \"%.1f\", ($GZIP_BYTES / $OPTIMIZED_BYTES) * 100}")
            print_success "Gzip compressed size: $GZIP_SIZE ($GZIP_PERCENT% of optimized size)"
            TOTAL_GZIP_BYTES=$((TOTAL_GZIP_BYTES + GZIP_BYTES))
        else
            print_warning "Gzip compression failed"
        fi

        # Compress with brotli if available
        if command -v brotli &> /dev/null; then
            print_info "Compressing WASM file with brotli..."
            if brotli -9 -f -k "$WASM_FILE" 2>/dev/null; then
                BROTLI_BYTES=$(stat -c %s "${WASM_FILE}.br" 2>/dev/null || stat -f %z "${WASM_FILE}.br" 2>/dev/null)
                BROTLI_SIZE=$(du -h "${WASM_FILE}.br" 2>/dev/null | cut -f1)
                BROTLI_PERCENT=$(awk "BEGIN {printf \"%.1f\", ($BROTLI_BYTES / $OPTIMIZED_BYTES) * 100}")
                print_success "Brotli compressed size: $BROTLI_SIZE ($BROTLI_PERCENT% of optimized size)"
                TOTAL_BROTLI_BYTES=$((TOTAL_BROTLI_BYTES + BROTLI_BYTES))
            else
                print_warning "Brotli compression failed"
            fi
        else
            print_warning "Brotli not found. Skipping brotli compression."
        fi
    done

    # Calculate human-readable total sizes
    TOTAL_ORIGINAL_SIZE=$(echo "$TOTAL_ORIGINAL_BYTES" | awk '{ suffix="KMGT"; for(i=1; $1>1024 && i<length(suffix); i++) $1/=1024; printf "%.1f%s", $1, substr(suffix,i,1); }')
    TOTAL_OPTIMIZED_SIZE=$(echo "$TOTAL_OPTIMIZED_BYTES" | awk '{ suffix="KMGT"; for(i=1; $1>1024 && i<length(suffix); i++) $1/=1024; printf "%.1f%s", $1, substr(suffix,i,1); }')
    TOTAL_GZIP_SIZE=$(echo "$TOTAL_GZIP_BYTES" | awk '{ suffix="KMGT"; for(i=1; $1>1024 && i<length(suffix); i++) $1/=1024; printf "%.1f%s", $1, substr(suffix,i,1); }')
    TOTAL_BROTLI_SIZE=$(echo "$TOTAL_BROTLI_BYTES" | awk '{ suffix="KMGT"; for(i=1; $1>1024 && i<length(suffix); i++) $1/=1024; printf "%.1f%s", $1, substr(suffix,i,1); }')

    # Calculate total reduction
    if [ $TOTAL_ORIGINAL_BYTES -gt 0 ]; then
        TOTAL_REDUCTION=$((TOTAL_ORIGINAL_BYTES - TOTAL_OPTIMIZED_BYTES))
        TOTAL_PERCENT=$(awk "BEGIN {printf \"%.1f\", ($TOTAL_REDUCTION / $TOTAL_ORIGINAL_BYTES) * 100}")
    else
        TOTAL_REDUCTION=0
        TOTAL_PERCENT=0
    fi

    print_success "WASM optimization completed successfully."
    print_info "Optimization summary:"
    print_info "- Total WASM files: $WASM_COUNT"
    print_info "- Optimized: $OPTIMIZED_COUNT"
    print_info "- Skipped (already optimized): $SKIPPED_COUNT"
    print_info "- Failed: $FAILED_COUNT"
    print_info "- Total original size: $TOTAL_ORIGINAL_SIZE ($TOTAL_ORIGINAL_BYTES bytes)"
    print_info "- Total optimized size: $TOTAL_OPTIMIZED_SIZE ($TOTAL_OPTIMIZED_BYTES bytes)"
    print_info "- Total reduction: $TOTAL_REDUCTION bytes ($TOTAL_PERCENT%)"
    print_info "- Total gzip size: $TOTAL_GZIP_SIZE ($TOTAL_GZIP_BYTES bytes)"
    print_info "- Total brotli size: $TOTAL_BROTLI_SIZE ($TOTAL_BROTLI_BYTES bytes)"

    return 0
}

# Main function
main() {
    # Parse command line arguments
    TARGET="${1:-dev}"
    SERVE=false
    COMMAND=""

    # Check for additional options
    for arg in "$@"; do
        case "$arg" in
            "--serve")
                SERVE=true
                ;;
            "--help"|"-h")
                show_usage
                exit 0
                ;;
        esac
    done

    # Check if the first argument is a command
    case "$TARGET" in
        "css")
            build_tailwind_css
            exit $?
            ;;
        "optimize-svg")
            optimize_svg
            exit $?
            ;;
        "optimize-wasm")
            optimize_wasm
            exit $?
            ;;
    esac

    # Configure build based on target
    case "$TARGET" in
        "dev"|"development")
            print_info "Building for development..."
            export TRUNK_PUBLIC_URL="/"
            export TRUNK_SERVE_PORT="8089"
            export TRUNK_RELEASE="false"
            BUILD_MODE="development"
            ;;
        "release"|"production")
            print_info "Building for production release..."
            export TRUNK_PUBLIC_URL="/"
            export TRUNK_SERVE_PORT="8081"
            export TRUNK_RELEASE="true"
            BUILD_MODE="production"
            ;;
        "github-pages"|"gh-pages")
            print_info "Building for GitHub Pages..."
            export TRUNK_PUBLIC_URL="/yew-it-tools/"
            export TRUNK_SERVE_PORT="8081"
            export TRUNK_RELEASE="true"
            BUILD_MODE="github-pages"
            ;;
        "help"|"-h"|"--help")
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown target: $TARGET"
            show_usage
            exit 1
            ;;
    esac

    print_info "Build configuration:"
    echo "  Target: $TARGET"
    echo "  Public URL: $TRUNK_PUBLIC_URL"
    echo "  Mode: $BUILD_MODE"
    echo "  Serve after build: $SERVE"

    # Check for required tools
    if ! command -v trunk &> /dev/null; then
        print_error "Trunk is not installed. Please install it with 'cargo install trunk'."
        exit 1
    fi

    # Build Tailwind CSS
    print_info "Building Tailwind CSS..."
    if ! build_tailwind_css; then
        print_error "Failed to build Tailwind CSS."
        exit 1
    fi

    # Build with Trunk
    print_info "Building with Trunk..."
    if [ "$BUILD_MODE" = "development" ]; then
        if ! trunk build --public-url "$TRUNK_PUBLIC_URL"; then
            print_error "Failed to build with Trunk."
            exit 1
        fi
    else
        if ! trunk build --release --public-url "$TRUNK_PUBLIC_URL"; then
            print_error "Failed to build with Trunk."
            exit 1
        fi
    fi

    # Display build stats
    print_info "Build statistics:"
    echo "-------------------------------------"
    echo "Total size: $(du -sh dist 2>/dev/null | cut -f1)"
    if [ -d "dist" ]; then
        wasm_files=$(find dist -name "*.wasm" 2>/dev/null | wc -l)
        js_files=$(find dist -name "*.js" 2>/dev/null | wc -l)
        css_files=$(find dist -name "*.css" 2>/dev/null | wc -l)

        if [ "$wasm_files" -gt 0 ]; then
            wasm_size=$(du -ch $(find dist -name "*.wasm" 2>/dev/null) 2>/dev/null | grep total | cut -f1)
            echo "WASM size: $wasm_size ($wasm_files files)"
        fi
        if [ "$js_files" -gt 0 ]; then
            js_size=$(du -ch $(find dist -name "*.js" 2>/dev/null) 2>/dev/null | grep total | cut -f1)
            echo "JS size: $js_size ($js_files files)"
        fi
        if [ "$css_files" -gt 0 ]; then
            css_size=$(du -ch $(find dist -name "*.css" 2>/dev/null) 2>/dev/null | grep total | cut -f1)
            echo "CSS size: $css_size ($css_files files)"
        fi

        # Check for compressed files
        gzip_files=$(find dist -name "*.gz" 2>/dev/null | wc -l)
        br_files=$(find dist -name "*.br" 2>/dev/null | wc -l)

        if [ "$gzip_files" -gt 0 ]; then
            gzip_size=$(du -ch $(find dist -name "*.gz" 2>/dev/null) 2>/dev/null | grep total | cut -f1)
            echo "Gzip compressed: $gzip_size ($gzip_files files)"
        fi
        if [ "$br_files" -gt 0 ]; then
            br_size=$(du -ch $(find dist -name "*.br" 2>/dev/null) 2>/dev/null | grep total | cut -f1)
            echo "Brotli compressed: $br_size ($br_files files)"
        fi
    fi
    echo "-------------------------------------"

    print_success "$TARGET build completed successfully!"
    print_success "The build output is in the 'dist' directory."

    # Serve the build if requested
    if [ "$SERVE" = true ]; then
        print_info "Starting server on port $TRUNK_SERVE_PORT..."
        if [ "$BUILD_MODE" = "development" ]; then
            trunk serve
        else
            trunk serve --release
        fi
    else
        if [ "$BUILD_MODE" = "github-pages" ]; then
            print_info "You can now deploy this directory to GitHub Pages manually if needed."
            print_info "Or push to the main branch to trigger automatic deployment via GitHub Actions."
        elif [ "$BUILD_MODE" = "development" ]; then
            print_info "You can serve this locally with 'trunk serve' or any static file server."
        else
            print_info "This build is ready for production deployment."
        fi
    fi
}

# Call the main function with all arguments
main "$@"