[package]
name = "yew-it-tools"
version = "1.0.0"
edition = "2024"
description = "IT Tools migrated to Rust/Yew"
authors = ["henri89"]
license = "LicenseRef-Proprietary"

[dependencies]
yew = { version = "0.21", features = ["csr"] }
wasm-bindgen = "0.2"
web-sys = { version = "0.3", features = [
    "Document",
    "Element",
    "HtmlElement",
    "HtmlIFrameElement",
    "Node",
    "Window",
    "Storage",
    "MediaQueryList",
    "HtmlInputElement",
    "HtmlTextAreaElement",
    "CssStyleDeclaration",
    "DomTokenList",
    "History",
    "Location",
    "console",
    "Request",
    "RequestInit",
    "RequestCredentials",
    "Response",
    "Headers",
    "Blob",
    "BlobPropertyBag",
    "Url",
    "DomParser",
    "SupportedType",
    "ServiceWorkerContainer",
    "ServiceWorkerRegistration",
    "ServiceWorker",
    "MessageChannel",
    "MessagePort",
    "Worker",
    "SharedWorker",
    "BroadcastChannel",
    "Cache",
    "CacheStorage",
    "Navigator",
    "Event"
]}
js-sys = "0.3"
gloo = { version = "0.11", features = ["storage", "events", "timers"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
yew-router = "0.18"
log = "0.4"
wasm-logger = "0.2"
base64 = "0.22"
chrono = { version = "0.4", features = ["wasmbind", "serde"] }
wasm-bindgen-futures = "0.4.50"
regex = "1.11.1"
urlencoding = "2.1.3"

# CLI testing dependencies
tokio = { version = "1.0", features = ["full", "fs"] }
reqwest = { version = "0.11", features = ["json", "gzip", "stream"] }
tokio-stream = "0.1"
flate2 = "1.0"
thiserror = "1.0"
bytes = "1.0"

[profile.release]
# Maximum optimization for size
opt-level = "z"
# Enable link-time optimization
lto = true
# Reduce parallel code generation units to increase optimization
codegen-units = 1
# Enable panic abort to reduce binary size
panic = "abort"
# Strip debug symbols
strip = true
# Optimize for size
debug = false
