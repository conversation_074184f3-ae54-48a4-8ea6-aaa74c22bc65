#!/bin/bash

# Enhanced PR Description Updater
# This script manually updates a PR description with CI/CD analysis results
# Usage: ./scripts/update-pr-description.sh <PR_NUMBER>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PR number is provided
if [ $# -eq 0 ]; then
    print_error "Usage: $0 <PR_NUMBER>"
    print_error "Example: $0 42"
    exit 1
fi

PR_NUMBER=$1

# Check if gh CLI is installed
if ! command -v gh &> /dev/null; then
    print_error "GitHub CLI (gh) is not installed. Please install it first."
    print_error "Visit: https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    print_error "You are not authenticated with GitHub CLI."
    print_error "Run: gh auth login"
    exit 1
fi

print_status "Updating PR #$PR_NUMBER description..."

# Get current PR details
print_status "Fetching PR details..."
if ! PR_DATA=$(gh api "/repos/:owner/:repo/pulls/$PR_NUMBER" 2>/dev/null); then
    print_error "Failed to fetch PR #$PR_NUMBER. Please check if the PR exists."
    exit 1
fi

CURRENT_TITLE=$(echo "$PR_DATA" | jq -r '.title')
CURRENT_BODY=$(echo "$PR_DATA" | jq -r '.body // ""')
PR_STATE=$(echo "$PR_DATA" | jq -r '.state')

print_status "PR Title: $CURRENT_TITLE"
print_status "PR State: $PR_STATE"

# Check if PR is open
if [ "$PR_STATE" != "open" ]; then
    print_warning "PR #$PR_NUMBER is not open (state: $PR_STATE)"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled."
        exit 0
    fi
fi

# Check if PR already has enhanced description
if echo "$CURRENT_BODY" | grep -q "<!-- AUTO-GENERATED SECTION -->"; then
    print_warning "PR already has an auto-generated section."
    read -p "Replace existing auto-generated content? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled."
        exit 0
    fi
    # Remove existing auto-generated section
    CURRENT_BODY=$(echo "$CURRENT_BODY" | sed '/<!-- AUTO-GENERATED SECTION -->/,$d')
fi

# Get commit messages for the PR
print_status "Analyzing commits..."
COMMIT_MESSAGES=$(gh api "/repos/:owner/:repo/pulls/$PR_NUMBER/commits" --jq '.[].commit.message' | sed 's/^/- /' | head -10)

# Get changed files
print_status "Analyzing changed files..."
CHANGED_FILES=$(gh api "/repos/:owner/:repo/pulls/$PR_NUMBER/files" --jq '.[].filename')

# Count file types
RUST_FILES=$(echo "$CHANGED_FILES" | grep -c "\.rs$" || echo "0")
CSS_FILES=$(echo "$CHANGED_FILES" | grep -c "\.css$" || echo "0")
HTML_FILES=$(echo "$CHANGED_FILES" | grep -c "\.html$" || echo "0")
CONFIG_FILES=$(echo "$CHANGED_FILES" | grep -c -E "\.(toml|json|yml|yaml|js|mjs)$" || echo "0")
DOC_FILES=$(echo "$CHANGED_FILES" | grep -c -E "\.(md|txt|rst)$" || echo "0")

# Determine change type
CHANGE_TYPE="other"
if echo "$CURRENT_TITLE" | grep -i "fix\|bug\|hotfix" > /dev/null; then
    CHANGE_TYPE="🐛 Bug Fix"
elif echo "$CURRENT_TITLE" | grep -i "feat\|feature\|add" > /dev/null; then
    CHANGE_TYPE="✨ Feature"
elif echo "$CURRENT_TITLE" | grep -i "perf\|performance\|optimize" > /dev/null; then
    CHANGE_TYPE="⚡ Performance"
elif echo "$CURRENT_TITLE" | grep -i "docs\|documentation" > /dev/null; then
    CHANGE_TYPE="📚 Documentation"
elif echo "$CURRENT_TITLE" | grep -i "refactor\|cleanup" > /dev/null; then
    CHANGE_TYPE="♻️ Refactoring"
elif echo "$CURRENT_TITLE" | grep -i "deps\|dependencies\|update" > /dev/null; then
    CHANGE_TYPE="📦 Dependencies"
fi

print_status "Detected change type: $CHANGE_TYPE"

# Generate enhanced description
print_status "Generating enhanced description..."

# Pre-calculate values to avoid complex command substitution in heredoc
ANALYSIS_TIME=$(date -u +"%Y-%m-%d %H:%M:%S UTC")

# Determine impact descriptions
RUST_IMPACT="➖ No changes"
if [ "$RUST_FILES" -gt 0 ]; then
    RUST_IMPACT="🔧 Code changes"
fi

CSS_IMPACT="➖ No changes"
if [ "$CSS_FILES" -gt 0 ]; then
    CSS_IMPACT="🎨 Styling changes"
fi

HTML_IMPACT="➖ No changes"
if [ "$HTML_FILES" -gt 0 ]; then
    HTML_IMPACT="📄 Template changes"
fi

CONFIG_IMPACT="➖ No changes"
if [ "$CONFIG_FILES" -gt 0 ]; then
    CONFIG_IMPACT="⚙️ Configuration changes"
fi

DOC_IMPACT="➖ No changes"
if [ "$DOC_FILES" -gt 0 ]; then
    DOC_IMPACT="📚 Documentation changes"
fi

# Create enhanced description using a more reliable method
cat > /tmp/enhanced_description.md << 'EOF'
<!-- AUTO-GENERATED SECTION -->
## 🚀 CI/CD Analysis Results

### ✅ Build Status
- **Status**: ✅ Manual analysis completed
- **Analysis Time**: ANALYSIS_TIME_PLACEHOLDER
- **Change Type**: CHANGE_TYPE_PLACEHOLDER

### 📊 File Analysis
| File Type | Count | Impact |
|-----------|-------|--------|
| Rust (.rs) | RUST_FILES_PLACEHOLDER | RUST_IMPACT_PLACEHOLDER |
| CSS (.css) | CSS_FILES_PLACEHOLDER | CSS_IMPACT_PLACEHOLDER |
| HTML (.html) | HTML_FILES_PLACEHOLDER | HTML_IMPACT_PLACEHOLDER |
| Config | CONFIG_FILES_PLACEHOLDER | CONFIG_IMPACT_PLACEHOLDER |
| Documentation | DOC_FILES_PLACEHOLDER | DOC_IMPACT_PLACEHOLDER |

### 📝 Changes Made
COMMIT_MESSAGES_PLACEHOLDER

### 📋 Quality Checklist
- [ ] Code builds successfully
- [ ] All tests pass
- [ ] Code formatting is correct (cargo fmt)
- [ ] No clippy warnings (cargo clippy)
- [ ] Security audit passed (cargo audit)
- [ ] Bundle size within limits
- [ ] Manual testing completed

### 🧪 Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All existing tests pass

### 🚀 Performance Impact
- [ ] No performance impact
- [ ] Performance improvement
- [ ] Potential performance regression (explain below)

### 🔒 Security Considerations
- [ ] No security implications
- [ ] Security improvement
- [ ] Potential security impact (explain below)

### 📚 Documentation
- [ ] Code is self-documenting
- [ ] Inline comments added where necessary
- [ ] README updated
- [ ] API documentation updated

---
*This analysis was manually generated using the PR automation script.*
EOF

# Replace placeholders with actual values
sed -i.bak \
    -e "s/ANALYSIS_TIME_PLACEHOLDER/$ANALYSIS_TIME/g" \
    -e "s/CHANGE_TYPE_PLACEHOLDER/$CHANGE_TYPE/g" \
    -e "s/RUST_FILES_PLACEHOLDER/$RUST_FILES/g" \
    -e "s/RUST_IMPACT_PLACEHOLDER/$RUST_IMPACT/g" \
    -e "s/CSS_FILES_PLACEHOLDER/$CSS_FILES/g" \
    -e "s/CSS_IMPACT_PLACEHOLDER/$CSS_IMPACT/g" \
    -e "s/HTML_FILES_PLACEHOLDER/$HTML_FILES/g" \
    -e "s/HTML_IMPACT_PLACEHOLDER/$HTML_IMPACT/g" \
    -e "s/CONFIG_FILES_PLACEHOLDER/$CONFIG_FILES/g" \
    -e "s/CONFIG_IMPACT_PLACEHOLDER/$CONFIG_IMPACT/g" \
    -e "s/DOC_FILES_PLACEHOLDER/$DOC_FILES/g" \
    -e "s/DOC_IMPACT_PLACEHOLDER/$DOC_IMPACT/g" \
    /tmp/enhanced_description.md

# Handle commit messages separately to avoid sed issues
if [ -n "$COMMIT_MESSAGES" ]; then
    # Create a temporary file with commit messages
    echo "$COMMIT_MESSAGES" > /tmp/commit_messages.txt
    # Use a simpler approach with sed
    sed '/COMMIT_MESSAGES_PLACEHOLDER/r /tmp/commit_messages.txt' /tmp/enhanced_description.md | \
    sed '/COMMIT_MESSAGES_PLACEHOLDER/d' > /tmp/enhanced_description_final.md
    mv /tmp/enhanced_description_final.md /tmp/enhanced_description.md
    rm -f /tmp/commit_messages.txt
else
    sed -i.bak 's/COMMIT_MESSAGES_PLACEHOLDER/- No commit messages found/g' /tmp/enhanced_description.md
fi

# Clean up backup file
rm -f /tmp/enhanced_description.md.bak

# Combine with existing description
if [ -n "$CURRENT_BODY" ] && [ "$CURRENT_BODY" != "null" ]; then
    echo "$CURRENT_BODY" > /tmp/original_description.md
    echo "" >> /tmp/original_description.md
    cat /tmp/enhanced_description.md >> /tmp/original_description.md
    mv /tmp/original_description.md /tmp/final_description.md
else
    mv /tmp/enhanced_description.md /tmp/final_description.md
fi

# Update the PR description using gh pr edit
print_status "Updating PR description..."
if gh pr edit "$PR_NUMBER" --body-file /tmp/final_description.md; then
    print_success "PR #$PR_NUMBER description updated successfully!"
    # Get the PR URL for display
    PR_URL=$(gh pr view "$PR_NUMBER" --json url --jq '.url')
    print_status "View the updated PR: $PR_URL"
else
    print_error "Failed to update PR description."
    print_error "You can manually copy the content from /tmp/final_description.md"
    exit 1
fi

# Cleanup
rm -f /tmp/enhanced_description.md /tmp/original_description.md /tmp/final_description.md

print_success "Operation completed successfully!"
