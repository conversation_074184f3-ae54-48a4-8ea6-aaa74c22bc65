[build]
# The index HTML file to drive the bundling process.
target = "index.html"
# Build in release mode (can be overridden by environment variable TRUNK_RELEASE)
release = true
# The output dir for all final assets.
dist = "dist"
# The public URL from which assets are to be served.
# Overridden by TRUNK_PUBLIC_URL environment variable in build.sh:
# - Development: TRUNK_PUBLIC_URL="/"
# - GitHub Pages: TRUNK_PUBLIC_URL="/yew-it-tools/"
# Note: This will be overridden by the environment variable during builds
public_url = "/"
# Enable hashing for better caching
filehash = true

# Assets are copied by the build.sh script

[watch]
# Paths to watch for changes - explicitly list only what we need to watch
watch = ["src/", "index.html", "Cargo.toml", "postcss.config.mjs"]
# Ignore dist directory and build artifacts to prevent rebuild loops
ignore = ["dist/", "target/", ".svg-optimization-cache", ".wasm-optimization-cache", "node_modules/"]

# Run the CSS build before Trunk builds
[[hooks]]
stage = "pre_build"
command = "./build.sh"
command_arguments = ["css"]

# Optimize SVG icons (only in release mode)
[[hooks]]
stage = "pre_build"
command = "bash"
command_arguments = ["-c", "if [ -z \"$TRUNK_SERVE_ENV\" ] || [ \"$TRUNK_RELEASE\" = \"true\" ]; then ./build.sh optimize-svg; else echo 'Development mode: Skipping SVG optimization'; fi"]

# Run optimize-wasm script to further optimize the WASM binary (only in release mode)
[[hooks]]
stage = "post_build"
command = "bash"
command_arguments = ["-c", "if [ -z \"$TRUNK_SERVE_ENV\" ] || [ \"$TRUNK_RELEASE\" = \"true\" ]; then ./build.sh optimize-wasm; else echo 'Development mode: Skipping WASM optimization'; fi"]

[serve]
# The addresses to serve on (can be overridden by environment variable TRUNK_SERVE_ADDRESS)
addresses = ["127.0.0.1"]
# The port to serve on (can be overridden by environment variable TRUNK_SERVE_PORT)
port = 8089
# Open a browser tab once the initial build is complete.
open = true
# Enable SPA fallback for client-side routing
spa = true

# Enable compression for all assets (only in release mode)
[[hooks]]
stage = "post_build"
command = "bash"
command_arguments = ["-c", "if [ -z \"$TRUNK_SERVE_ENV\" ] || [ \"$TRUNK_RELEASE\" = \"true\" ]; then find dist -type f \\( -name '*.js' -o -name '*.css' -o -name '*.html' \\) -exec gzip -9 -f -k {} \\; 2>/dev/null || true; else echo 'Development mode: Skipping gzip compression'; fi"]

[[hooks]]
stage = "post_build"
command = "bash"
command_arguments = ["-c", "if command -v brotli &>/dev/null && { [ -z \"$TRUNK_SERVE_ENV\" ] || [ \"$TRUNK_RELEASE\" = \"true\" ]; }; then find dist -type f \\( -name '*.js' -o -name '*.css' -o -name '*.html' \\) -exec brotli -9 -f -k {} \\; 2>/dev/null || true; else echo 'Development mode or brotli not found: Skipping brotli compression'; fi"]

# Create .nojekyll file for GitHub Pages (only when TRUNK_PUBLIC_URL contains 'yew-it-tools')
[[hooks]]
stage = "post_build"
command = "bash"
command_arguments = ["-c", "if [[ \"$TRUNK_PUBLIC_URL\" == */yew-it-tools/* ]]; then touch dist/.nojekyll && echo 'Created .nojekyll file for GitHub Pages'; fi"]


