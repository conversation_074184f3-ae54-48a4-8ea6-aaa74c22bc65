# Yew IT Tools

[![CI/CD Pipeline](https://github.com/naoNao89/yew-it-tools/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/naoNao89/yew-it-tools/actions/workflows/ci-cd.yml)

A Rust/Yew implementation of the IT Tools project, originally built with Vue.js.

## Project Setup

### Prerequisites

- Rust and Cargo (install via [rustup](https://rustup.rs/))
- Trunk (install with `cargo install trunk`)
- Node.js and pnpm (for CSS processing)

### Installation

```sh
# Install Node.js dependencies
pnpm install
```

### Development

```sh
# Start the development server with CSS watching and Trunk
pnpm dev

# Or run these separately:
# Build Tailwind CSS
pnpm build:css

# Watch for CSS changes
pnpm watch:css

# Start Trunk server
trunk serve
```

### Build for Production

```sh
# Build everything for production
pnpm build

# Or build separately:
# Build Tailwind CSS
pnpm build:css

# Build Rust application
trunk build --release
```

## Project Structure

- `/src` - Rust source code
  - `/components` - Reusable UI components
  - `/layouts` - Page layouts
  - `/pages` - Application pages
  - `/stores` - State management
  - `/ui` - UI component library
  - `/assets` - Static assets
- `/dist` - Build output (generated by Trunk)
- `/public` - Static files that will be copied to the dist directory

## Features

- Dark/Light theme support
- Responsive design
- Tool favorites
- Tailwind CSS styling with PostCSS and Autoprefixer

## CSS Processing

This project uses:
- **Tailwind CSS**: Utility-first CSS framework (version 3)
- **PostCSS**: CSS transformation tool
- **Autoprefixer**: Adds vendor prefixes to CSS

The CSS processing pipeline is set up to:
1. Process the Tailwind directives in `src/tailwind.css`
2. Apply PostCSS transformations
3. Add vendor prefixes with Autoprefixer
4. Output the final CSS to `dist/tailwind.css`

## CI/CD Pipeline

This project uses GitHub Actions for continuous integration and deployment with comprehensive quality gates:

### Continuous Integration
- **Lint and Code Quality**:
  - Rust code formatting (`cargo fmt`)
  - Rust linting (`cargo clippy`)
  - Rust compilation checks (`cargo check`)
  - CSS/PostCSS configuration validation
- **Security and Vulnerability Scanning**:
  - Rust dependency audit (`cargo audit`)
  - License compliance checking (`cargo deny`)
  - Node.js dependency audit (`pnpm audit`)
- **Test and Coverage**:
  - Unit test execution (`cargo test`)
  - Code coverage analysis (`cargo llvm-cov`)
  - Coverage reporting to Codecov
- **Build and Performance Analysis**:
  - Successful build verification
  - Bundle size regression testing
  - Performance metrics analysis
  - Build artifact optimization

### Bundle Size Monitoring
The CI/CD pipeline enforces bundle size limits to prevent performance regressions:
- **WASM Bundle**: 2MB (2048KB) maximum
- **JavaScript Bundle**: 512KB maximum
- **CSS Bundle**: 256KB maximum

### Continuous Deployment
- **Automatic Deployment**: Deploys to GitHub Pages when changes are pushed to the main branch
- **Optimized Build**: Uses production build settings with WASM and asset optimization
- **Dependency Updates**: Uses Dependabot to keep dependencies up to date
- **Performance Monitoring**: Tracks bundle sizes and loading performance

### GitHub Pages
The project is automatically deployed to GitHub Pages at: https://naonao89.github.io/yew-it-tools/

### Branch Protection and Development Workflow

This project implements robust branch protection rules to prevent bugs from reaching production:

#### Branch Protection Rules
- **main**: Production branch with strict protection
  - Requires pull request reviews (minimum 1 reviewer)
  - All status checks must pass before merging
  - Branches must be up to date before merging
  - Direct pushes are restricted
- **dev**: Development branch with similar protections
  - Requires pull request reviews
  - All status checks must pass before merging

#### Required Status Checks
All pull requests must pass these automated checks:
1. **Lint and Code Quality** - Code formatting and quality standards
2. **Security and Vulnerability Scan** - Dependency security validation
3. **Test and Coverage** - Unit tests and coverage requirements
4. **Build and Performance Analysis** - Build success and bundle size limits

#### Development Workflow
1. Create feature/fix branches from `dev`
2. Make changes and push to your feature/fix branch
3. Create a pull request to merge into `dev`
4. Ensure all status checks pass and get code review approval
5. After testing in `dev`, create a pull request to merge into `main`
6. Merging to `main` automatically deploys to GitHub Pages

For detailed branch protection setup instructions, see [docs/BRANCH_PROTECTION.md](docs/BRANCH_PROTECTION.md).

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines on:
- Development workflow and branch strategy
- Code quality standards and testing requirements
- Performance guidelines and bundle size optimization
- Security considerations and best practices
- Pull request process and review requirements

## 🤖 Enhanced CI/CD & PR Automation

This project features an advanced CI/CD pipeline with automated PR management:

### Automated Features
- **Smart PR Analysis**: Automatically detects change types and analyzes impact
- **Dynamic Descriptions**: Updates PR descriptions with CI/CD results and metrics
- **Bundle Size Tracking**: Monitors and reports on build size changes
- **Quality Checks**: Automated formatting, linting, and security scans
- **Auto-Labeling**: Applies relevant labels based on change analysis

### Manual Tools
```bash
# Update PR description manually (requires GitHub CLI)
./scripts/update-pr-description.sh <PR_NUMBER>

# Example: Update PR #42
./scripts/update-pr-description.sh 42
```

The automation system provides comprehensive analysis including bundle size metrics, code quality checks, security scans, and performance impact assessments - all automatically integrated into your PR descriptions!

## Documentation

### CI/CD and Automation
- **[Enhanced CI/CD Guide](docs/ENHANCED_CI_CD_GUIDE.md)** - Complete automation documentation

### Development Workflow
- [AI Agent Instructions](docs/AI_AGENT_INSTRUCTIONS.md) - **Essential rules and preferences for AI agents** ⭐
- [Agent Workflow Guide](docs/AGENT_WORKFLOW_GUIDE.md) - Comprehensive GitHub workflow instructions for AI agents
- [Quick Reference](docs/AGENT_WORKFLOW_QUICK_REFERENCE.md) - Quick commands and conventions reference
- [Pull Request Template](docs/PULL_REQUEST_TEMPLATE.md) - Standard PR template for consistent submissions

### Project Setup and Configuration
- [Branch Protection Setup](docs/BRANCH_PROTECTION.md) - Configure GitHub branch protection rules
- [Bundle Size Optimization](docs/OPTIMIZATION.md) - Performance optimization strategies
- [Contributing Guidelines](CONTRIBUTING.md) - Development workflow and standards

### For AI Agents
This project includes comprehensive workflow documentation specifically designed for AI agents working on the codebase. The workflow ensures:
- Consistent issue tracking and branch management
- Proper commit message formatting and PR descriptions
- Quality gates and testing requirements
- Professional communication standards

See the [Agent Workflow Guide](docs/AGENT_WORKFLOW_GUIDE.md) for complete instructions.
