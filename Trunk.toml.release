[build]
# The index HTML file to drive the bundling process.
target = "index.html"
# Build in release mode.
release = true
# The output dir for all final assets.
dist = "dist"
# The public URL from which assets are to be served.
public_url = "/"
# Enable hashing for better caching
filehash = true

# Assets are copied by the build-tailwind.sh script

[watch]
# Paths to watch for changes
watch = ["src/", "src/assets/", "index.html", "Cargo.toml", "tailwind.config.js", "postcss.config.js"]

# Run the CSS build before Trunk builds
[[hooks]]
stage = "pre_build"
command = "./build-tailwind.sh"

# Run optimize-wasm.sh script to further optimize the WASM binary
[[hooks]]
stage = "post_build"
command = "./optimize-wasm.sh"

[serve]
# The address to serve on.
address = "127.0.0.1"
# The port to serve on.
port = 8081
# Open a browser tab once the initial build is complete.
open = true

# Enable compression for all assets
[[hooks]]
stage = "post_build"
command = "find"
command_arguments = ["dist", "-type", "f", "-name", "*.js", "-o", "-name", "*.css", "-o", "-name", "*.wasm", "-o", "-name", "*.html", "-exec", "gzip", "-9", "-k", "{}", ";"]

[[hooks]]
stage = "post_build"
command = "find"
command_arguments = ["dist", "-type", "f", "-name", "*.js", "-o", "-name", "*.css", "-o", "-name", "*.wasm", "-o", "-name", "*.html", "-exec", "brotli", "-9", "{}", ";"]
