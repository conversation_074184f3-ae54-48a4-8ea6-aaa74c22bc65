<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT Tools - Handy online tools for developers</title>
    <meta name="description" content="Collection of handy online tools for developers, with great UX. IT Tools is a free and open-source collection of handy online tools for developers & people working in IT.">
    <meta itemprop="name" content="IT Tools - Handy online tools for developers">
    <meta itemprop="description" content="Collection of handy online tools for developers, with great UX. IT Tools is a free and open-source collection of handy online tools for developers & people working in IT.">
    <link rel="author" href="./public/humans.txt">
    <link rel="canonical" href="https://it-tools.tech">

    <!-- Favicons -->
    <link rel="apple-touch-icon" sizes="180x180" href="./public/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="./public/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./public/favicon-16x16.png">
    <link rel="icon" href="./public/favicon.ico">
    <link rel="mask-icon" href="./public/safari-pinned-tab.svg" color="#18a058">
    <meta name="msapplication-TileColor" content="#18a058">
    <meta name="msapplication-TileImage" content="./public/mstile-144x144.png">
    <meta name="theme-color" content="#ffffff">

    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://it-tools.tech/">
    <meta property="og:title" content="IT Tools - Handy online tools for developers">
    <meta property="og:description" content="Collection of handy online tools for developers, with great UX. IT Tools is a free and open-source collection of handy online tools for developers & people working in IT.">
    <meta property="og:image" content="./public/banner.png">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@ittoolsdottech">
    <meta name="twitter:creator" content="@cthmsst">
    <meta name="twitter:title" content="IT Tools - Handy online tools for developers">
    <meta name="twitter:description" content="Collection of handy online tools for developers, with great UX. IT Tools is a free and open-source collection of handy online tools for developers & people working in IT.">
    <meta name="twitter:image" content="./public/banner.png">
    <meta name="twitter:image:alt" content="IT Tools - Handy online tools for developers">

    <!-- Prevent flash of unstyled content by applying theme before page renders -->
    <script>
        (function() {
            // Check for saved theme preference or default to system preference
            const savedTheme = localStorage.getItem('isDarkTheme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const isDarkTheme = savedTheme !== null ? JSON.parse(savedTheme) : prefersDark;

            // Apply theme class immediately to prevent flash
            const html = document.documentElement;
            if (isDarkTheme) {
                html.classList.add('dark');
                html.classList.remove('light');
            } else {
                html.classList.add('light');
                html.classList.remove('dark');
            }
        })();
    </script>

    <!-- Tailwind CSS is loaded from the dist directory -->
    <style>
        /* Override Tailwind preflight CSS for SVG elements */
        svg {
            display: inline !important;
            vertical-align: initial !important;
        }

        /* Base styles */
        body {
            min-height: 100%;
            margin: 0;
            padding: 0;
            color: #1f2937;
            /* Prevent flash during theme transitions */
            transition: background-color 0.2s ease, color 0.2s ease;
        }

        html {
            height: 100%;
            margin: 0;
            padding: 0;
            /* Ensure theme classes are applied immediately */
            transition: color-scheme 0.2s ease;
        }

        * {
            box-sizing: border-box;
        }

        .dark {
            color-scheme: dark;
        }

        .dark body {
            color: #e5e7eb;
        }

        /* Ensure light theme has explicit background */
        .light body {
            background-color: #f3f4f6;
            color: #1f2937;
        }

        /* Ensure dark theme has explicit background */
        .dark body {
            background-color: #1c1c1c;
            color: #e5e7eb;
        }

        /* Layout components */
        .menu-layout-container {
            display: flex;
            height: 100vh;
        }

        .menu-layout-container.menu-collapsed .sider-container {
            width: 0;
            overflow: hidden;
        }

        .sider-container {
            width: 240px;
            background-color: #232323;
            color: white;
            transition: all 0.3s ease-in-out;
            border-right: 1px solid #333;
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 20;
            overflow-y: auto;
        }

        .content-container {
            flex: 1;
            margin-left: 240px;
            transition: all 0.3s ease-in-out;
            width: calc(100% - 240px);
            background-color: #f3f4f6;
            overflow: auto;
            display: flex;
            flex-direction: column;
        }

        .menu-layout-container.menu-collapsed .content-container {
            margin-left: 0;
            width: 100%;
        }

        .dark .content-container {
            background-color: #1c1c1c;
        }

        .main-content {
            flex: 1;
            padding: 0 26px 26px 26px;
        }

        /* Overlay for small screens */
        .menu-overlay {
            position: fixed;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 15;
            cursor: pointer;
            display: none;
        }

        .menu-layout-container.isSmallScreen:not(.menu-collapsed) .menu-overlay {
            display: block;
        }

        /* Mobile styles */
        @media (max-width: 700px) {
            .sider-container {
                position: fixed;
                width: 85%;
                max-width: 300px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .menu-layout-container.isSmallScreen:not(.menu-collapsed) .sider-container {
                transform: translateX(0);
            }

            .content-container {
                margin-left: 0;
                width: 100%;
            }
        }

        /* Hero section */
        .hero-wrapper {
            position: absolute;
            display: block;
            left: 0;
            width: 100%;
            z-index: 10;
            overflow: hidden;
            text-decoration: none;
            color: white;
        }

        .gradient {
            width: 100%;
            height: 160px;
            margin-top: -65px;
        }

        .text-wrapper {
            position: absolute;
            left: 0;
            width: 100%;
            text-align: center;
            top: 16px;
            color: #fff;
            z-index: 11;
        }

        .title {
            font-size: 25px;
            font-weight: 600;
        }

        .divider {
            width: 50px;
            height: 2px;
            border-radius: 4px;
            background-color: #18a058;
            margin: 0 auto 5px;
        }

        .subtitle {
            font-size: 16px;
        }

        .footer {
            text-align: center;
            color: #838587;
            margin-top: 20px;
            padding: 20px 0;
        }

        /* Button components */
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: color 0.2s, background-color 0.2s;
            outline: none;
            border: none;
            cursor: pointer;
        }

        .btn-text {
            background-color: transparent;
        }

        .support-button {
            background: linear-gradient(48deg, rgba(37, 99, 108, 1) 0%, rgba(59, 149, 111, 1) 60%, rgba(20, 160, 88, 1) 100%);
            color: #fff !important;
            transition: padding ease 0.2s !important;
            border-radius: 9999px;
        }

        .support-button:hover {
            color: #fff;
            padding-left: 30px;
            padding-right: 30px;
        }

        .btn-primary {
            background-color: #22c55e;
            color: white;
        }

        .btn-primary:hover {
            background-color: #16a34a;
        }

        .btn-default {
            background-color: #e5e7eb;
            color: #1f2937;
        }

        .btn-default:hover {
            background-color: #d1d5db;
        }

        .dark .btn-default {
            background-color: #374151;
            color: #e5e7eb;
        }

        .dark .btn-default:hover {
            background-color: #4b5563;
        }

        .btn-text {
            background-color: transparent;
        }

        .btn-text:hover {
            background-color: #e5e7eb;
        }

        .dark .btn-text:hover {
            background-color: #374151;
        }

        .btn-circle {
            border-radius: 9999px;
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Tool card */
        .tool-card {
            background-color: white;
            border-radius: 0.25rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            height: 100%;
        }

        /* Tool card icon styles - using SVG icons only */
        .tool-card-icon {
            color: #9ca3af; /* text-neutral-400 */
        }

        .dark .tool-card-icon {
            color: #525252; /* dark:text-neutral-600 */
        }

        .tool-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-color: #18a058;
        }

        .dark .tool-card {
            background-color: #232323;
            border-color: #282828;
        }

        .dark .tool-card:hover {
            border-color: #18a058;
        }

        .tool-card-content {
            padding: 1.25rem;
        }

        .tool-card-title {
            font-size: 1.125rem;
            font-weight: 500;
            margin: 0.5rem 0;
            color: #111827;
        }

        .dark .tool-card-title {
            color: white;
        }

        .tool-card-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .dark .tool-card-description {
            color: #d1d5db;
        }

        .tool-card-category {
            font-size: 0.75rem;
            color: #9ca3af;
            margin-top: 0.5rem;
        }

        .dark .tool-card-category {
            color: #9ca3af;
        }

        /* Colored card for banner */
        .colored-card {
            background: linear-gradient(48deg, rgba(37, 99, 108, 1) 0%, rgba(59, 149, 111, 1) 60%, rgba(20, 160, 88, 1) 100%);
            color: #fff;
            border: none;
        }

        .colored-card a {
            color: inherit;
            text-decoration: underline;
            font-weight: bold;
            transition: color ease 0.2s;
        }

        .colored-card a:hover {
            color: rgb(20, 20, 20);
        }

        /* Transition animations */
        .transition-height {
            transition: all 0.5s ease-in-out;
            overflow: hidden;
            max-height: 500px;
        }

        /* Tooltip */
        .tooltip-wrapper {
            position: relative;
            display: inline-block;
        }

        .tooltip-wrapper:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 10;
            margin-bottom: 0.25rem;
        }

        /* Grid layout */
        .grid-wrapper {
            padding: 0.5rem 1rem;
        }

        /* Utility classes */
        .decoration-none {
            text-decoration: none;
        }

        .line-clamp-2 {
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .bg-primary {
            background-color: #22c55e;
        }

        .text-primary {
            color: #22c55e;
        }

        .bg-opacity-10 {
            --tw-bg-opacity: 0.1;
        }

        /* Flexbox utilities */
        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-center {
            justify-content: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        /* Grid utilities */
        .grid {
            display: grid;
        }

        .grid-cols-1 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }

        .gap-12px {
            gap: 12px;
        }

        /* Spacing utilities */
        .mb-5px {
            margin-bottom: 5px;
        }

        .mt-25px {
            margin-top: 25px;
        }

        .pt-50px {
            padding-top: 50px;
        }

        .my-1 {
            margin-top: 0.25rem;
            margin-bottom: 0.25rem;
        }

        /* Text utilities */
        .text-lg {
            font-size: 1.125rem;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-xs {
            font-size: 0.75rem;
        }

        .text-4xl {
            font-size: 2.25rem;
        }

        .text-black {
            color: black;
        }

        .text-white {
            color: white;
        }

        .text-neutral-400 {
            color: #a3a3a3;
        }

        .text-neutral-500 {
            color: #737373;
        }

        .dark .text-neutral-400 {
            color: #a3a3a3;
        }

        .dark .text-neutral-500 {
            color: #737373;
        }

        .text-neutral-600 {
            color: #525252;
        }

        .dark .text-neutral-600 {
            color: #525252;
        }

        .font-500 {
            font-weight: 500;
        }

        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Responsive utilities */
        @media (min-width: 640px) {
            .sm\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 768px) {
            .md\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .lg\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        @media (min-width: 1280px) {
            .xl\:grid-cols-4 {
                grid-template-columns: repeat(4, minmax(0, 1fr));
            }
        }
    </style>

    <!-- Trunk Assets -->
    <link data-trunk rel="copy-dir" href="public">
    <link data-trunk rel="copy-dir" href="src/assets">
    <link data-trunk rel="css" href="dist/tailwind.css">
    <base data-trunk-public-url />
</head>
<body>
    <div id="app"></div>
</body>
</html>
