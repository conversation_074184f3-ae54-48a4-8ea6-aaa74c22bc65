#!/bin/bash
# Don't use set -e to avoid script termination on errors

# Check if we're in development mode (TRUNK_SERVE_ENV is set)
if [ -n "$TRUNK_SERVE_ENV" ]; then
    echo "Development mode: Skipping SVG optimization to avoid rebuild loops"
    exit 0
fi

echo "Starting SVG optimization..."

# Check if svgo is installed
if ! command -v svgo &> /dev/null; then
    echo "svgo not found. Skipping SVG optimization."
    echo "To enable SVG optimization, install SVGO manually with: npm install -g svgo"
    exit 0
fi

# Check if the icons directory exists
if [ ! -d "src/assets/icons" ]; then
    echo "Icons directory not found. Skipping SVG optimization."
    exit 0
fi

# Find all SVG files in the src/assets/icons directory
SVG_FILES=$(find src/assets/icons -name "*.svg" 2>/dev/null || echo "")

if [ -z "$SVG_FILES" ]; then
    echo "No SVG files found in src/assets/icons directory."
    exit 0
fi

echo "Found $(echo "$SVG_FILES" | wc -l) SVG files to optimize."

# Create a cache file to track optimized SVGs
CACHE_FILE=".svg-optimization-cache"
touch "$CACHE_FILE" 2>/dev/null

# Optimize each SVG file
for SVG_FILE in $SVG_FILES; do
    # Get the hash of the SVG file to check if it's already been optimized
    SVG_HASH=$(md5sum "$SVG_FILE" 2>/dev/null | cut -d ' ' -f 1)
    SVG_CACHE_ENTRY="${SVG_FILE}:${SVG_HASH}"

    # Skip if this exact file has already been optimized
    if grep -q "$SVG_CACHE_ENTRY" "$CACHE_FILE" 2>/dev/null; then
        echo "Skipping already optimized $SVG_FILE"
        continue
    fi

    echo "Optimizing $SVG_FILE..."

    # Skip if file doesn't exist
    if [ ! -f "$SVG_FILE" ]; then
        echo "Warning: File $SVG_FILE not found, skipping."
        continue
    fi

    # Create a temporary file for optimization
    TEMP_FILE=$(mktemp)

    # Copy the original file to the temporary file
    cp "$SVG_FILE" "$TEMP_FILE"

    # Optimize the SVG file with svgo
    if svgo -i "$TEMP_FILE" -o "$TEMP_FILE.opt" --pretty 2>/dev/null; then
        # If optimization was successful, replace the original file
        if cmp -s "$TEMP_FILE.opt" "$SVG_FILE"; then
            echo "No changes needed for $SVG_FILE"
        else
            cp "$TEMP_FILE.opt" "$SVG_FILE" 2>/dev/null || echo "Warning: Could not update $SVG_FILE"
        fi

        # Mark this file as optimized
        echo "$SVG_CACHE_ENTRY" >> "$CACHE_FILE"
    else
        echo "Warning: SVG optimization failed for $SVG_FILE, skipping."
    fi

    # Clean up temporary files
    rm -f "$TEMP_FILE" "$TEMP_FILE.opt" 2>/dev/null
done

echo "SVG optimization completed successfully."
exit 0
