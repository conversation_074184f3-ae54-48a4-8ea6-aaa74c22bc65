{"name": "yew-it-tools", "version": "1.0.0", "description": "IT Tools migrated to Rust/Yew", "private": true, "scripts": {"start": "./build.sh dev --serve", "dev": "./build.sh dev --serve", "build": "./build.sh release", "build:dev": "./build.sh dev", "build:release": "./build.sh release", "build:github-pages": "./build.sh github-pages", "css": "./build.sh css", "watch:css": "postcss ./src/tailwind.css -o ./dist/tailwind.css --watch", "optimize:svg": "./build.sh optimize-svg", "optimize:wasm": "./build.sh optimize-wasm", "clean": "rm -rf dist target .build-lock .svg-optimization-lock .wasm-optimization-cache .svg-optimization-cache", "analyze": "du -sh dist/* && find dist -type f -name '*.wasm' -o -name '*.js' -o -name '*.css' | xargs ls -lh"}, "devDependencies": {"autoprefixer": "^10.4.21", "cssnano": "^7.0.7", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "tailwindcss": "^4.1.7", "@tailwindcss/postcss": "^4.1.7", "terser": "^5.39.2"}}